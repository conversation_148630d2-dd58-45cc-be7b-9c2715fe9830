import { AIModeType } from '../contexts/AIModeContext';
import { MoodType, EmotionType } from '../contexts/EmotionalContext';

export interface ModeResponse {
  text: string;
  emotion: EmotionType;
  followUp?: string;
}

export class AIModeResponseEngine {
  private creativeResponses: ModeResponse[] = [
    {
      text: "🎨 Oh, I can feel the creative energy flowing! Let's paint the world with imagination! What kind of story shall we create together? A magical adventure? A tale of friendship? Or maybe something completely unique from your wonderful mind! ✨",
      emotion: 'excited',
      followUp: "Want to start with 'Once upon a time...' or shall we create our own beginning?"
    },
    {
      text: "🌈 Your creativity is like a rainbow after the rain - beautiful and full of possibilities! I'm here to help you express whatever is bubbling up in your imagination. Whether it's drawing, writing, or dreaming up new worlds! 🎭",
      emotion: 'excited',
      followUp: "What creative project is calling to your heart today?"
    },
    {
      text: "✨ In Creative Mode, there are no wrong answers - only wonderful discoveries waiting to happen! Your imagination is the most powerful tool in the universe. Let's use it to create something amazing together! 🚀",
      emotion: 'excited',
      followUp: "Shall we write a poem, design a character, or build a fantasy world?"
    }
  ];

  private calmResponses: ModeResponse[] = [
    {
      text: "🌸 Let's take a moment to breathe together... *inhale slowly for 4 counts... hold for 4... exhale gently for 6...* Feel your shoulders relax, your mind becoming peaceful like a still pond. You're safe here with me. 💙",
      emotion: 'comforting',
      followUp: "Would you like to try another breathing exercise, or shall we find a quiet activity together?"
    },
    {
      text: "🕯️ In this calm space, time moves gently like clouds drifting across a summer sky. There's no rush, no pressure - just you, me, and this peaceful moment. Let your worries float away like dandelion seeds on a soft breeze. 🌿",
      emotion: 'comforting',
      followUp: "What would help you feel even more relaxed right now?"
    },
    {
      text: "🌙 Imagine we're sitting by a gentle stream, listening to the soft sound of water flowing over smooth stones. Your mind can rest here, like a tired bird finding the perfect branch. Everything is okay. You are okay. 💚",
      emotion: 'comforting',
      followUp: "Would you like me to guide you through a short meditation, or shall we just sit quietly together?"
    }
  ];

  private puzzleResponses: ModeResponse[] = [
    {
      text: "🧩 Puzzle time! Here's a fun riddle for your brilliant mind: I have keys but no locks, I have space but no room, you can enter but not go inside. What am I? Take your time thinking - the best part is the 'aha!' moment! 🤔",
      emotion: 'curious',
      followUp: "Want a hint, or would you like to try a different type of puzzle?"
    },
    {
      text: "🎯 Ready for a brain teaser? If you have a 3-gallon jug and a 5-gallon jug, how can you measure exactly 4 gallons of water? Your problem-solving skills are amazing - I believe in you! 💪",
      emotion: 'thinking',
      followUp: "Should I give you a hint, or would you prefer a word puzzle instead?"
    },
    {
      text: "🔍 Detective mode activated! Here's a mystery: A man lives on the 20th floor of an apartment building. Every morning he takes the elevator down to the ground floor. When he comes home, he takes the elevator to the 10th floor and walks the rest... except on rainy days. Why? 🕵️",
      emotion: 'curious',
      followUp: "Need a clue, or shall we try a different kind of puzzle?"
    }
  ];

  private inspireResponses: ModeResponse[] = [
    {
      text: "🌟 Here's your daily spark of inspiration: 'You are braver than you believe, stronger than you seem, and smarter than you think.' Every challenge you face is just practice for becoming the amazing person you're meant to be! ✨",
      emotion: 'excited',
      followUp: "What's one small step you could take today toward something that excites you?"
    },
    {
      text: "💫 Remember, every expert was once a beginner, every pro was once an amateur, every icon was once an unknown. Your journey is just beginning, and I can't wait to see all the wonderful things you'll accomplish! 🚀",
      emotion: 'excited',
      followUp: "What dream or goal has been whispering to your heart lately?"
    },
    {
      text: "🌈 You know what's amazing? You've already overcome 100% of your worst days. That's not luck - that's your incredible strength! Today is a new page in your story, and you get to write it however you want. ✍️",
      emotion: 'excited',
      followUp: "What would make today feel like a victory, no matter how small?"
    }
  ];

  getModeResponse(mode: AIModeType, userMessage?: string, userMood?: MoodType): ModeResponse {
    switch (mode) {
      case 'creative':
        return this.getRandomResponse(this.creativeResponses);
      
      case 'calm':
        return this.getRandomResponse(this.calmResponses);
      
      case 'puzzle':
        return this.getRandomResponse(this.puzzleResponses);
      
      case 'inspire':
        return this.getRandomResponse(this.inspireResponses);
      
      default:
        return {
          text: "I'm here and ready to chat! What's on your mind today? 😊",
          emotion: 'happy'
        };
    }
  }

  private getRandomResponse(responses: ModeResponse[]): ModeResponse {
    return responses[Math.floor(Math.random() * responses.length)];
  }

  getModeGreeting(mode: AIModeType): ModeResponse {
    const greetings: Record<AIModeType, ModeResponse> = {
      default: {
        text: "Hi there! I'm back to my regular self - ready for whatever you need! 😊",
        emotion: 'happy'
      },
      creative: {
        text: "🎨 Creative Mode activated! I'm feeling super imaginative and ready to create something amazing with you! What shall we dream up together? ✨",
        emotion: 'excited'
      },
      calm: {
        text: "😌 Calm Mode engaged... *takes a deep, peaceful breath* I'm here to help you find tranquility and relaxation. Let's create a peaceful space together. 🌸",
        emotion: 'comforting'
      },
      puzzle: {
        text: "🧩 Puzzle Mode is ON! My brain is buzzing with riddles, games, and challenges! Ready to exercise those amazing thinking skills of yours? Let's have some fun! 🎯",
        emotion: 'curious'
      },
      inspire: {
        text: "💡 Inspire Mode activated! I'm overflowing with motivation and positive energy! Today is full of possibilities, and I'm here to help you see just how incredible you are! 🌟",
        emotion: 'excited'
      }
    };

    return greetings[mode];
  }

  getContextualResponse(mode: AIModeType, userMessage: string, userMood?: MoodType): ModeResponse {
    const message = userMessage.toLowerCase();
    
    // Mode-specific contextual responses
    if (mode === 'creative') {
      if (message.includes('story') || message.includes('write')) {
        return {
          text: "📚 A story! Oh, I love stories! Let's create characters that feel real, places that spark wonder, and adventures that make hearts race! What genre calls to your creative soul? Fantasy? Mystery? Adventure? Or something completely new? 🌟",
          emotion: 'excited',
          followUp: "Should we start with a character, a setting, or a magical object?"
        };
      }
      if (message.includes('draw') || message.includes('art')) {
        return {
          text: "🎨 Art is the language of the soul! Whether you're sketching with pencils, painting with colors, or creating digital masterpieces, every stroke tells a story. What's inspiring your artistic vision today? 🖌️",
          emotion: 'excited',
          followUp: "Want some creative prompts, or shall we explore different art techniques?"
        };
      }
    }

    if (mode === 'calm') {
      if (message.includes('stress') || message.includes('worried') || message.includes('anxious')) {
        return {
          text: "🌊 I can sense you need some gentle peace right now. Let's imagine stress as clouds passing through the sky of your mind - they come, they go, but the sky remains vast and beautiful. You are that sky - eternal and peaceful. 💙",
          emotion: 'comforting',
          followUp: "Would you like to try a 5-minute breathing exercise, or shall we do some gentle visualization?"
        };
      }
    }

    if (mode === 'puzzle') {
      if (message.includes('easy') || message.includes('hard')) {
        const difficulty = message.includes('easy') ? 'gentle' : 'challenging';
        return {
          text: `🎯 Perfect! I have some ${difficulty} puzzles that will be just right for your brilliant mind! Remember, every puzzle is a chance to discover how clever you really are! 🧠`,
          emotion: 'curious',
          followUp: `Ready for a ${difficulty} brain teaser?`
        };
      }
    }

    if (mode === 'inspire') {
      if (message.includes('sad') || message.includes('down') || message.includes('bad day')) {
        return {
          text: "🌈 Even on cloudy days, the sun is still shining above the clouds. Your light is still there too, even when you can't feel it. Bad days don't last, but resilient people like you do. You've gotten through tough times before, and you'll shine through this one too! ✨",
          emotion: 'comforting',
          followUp: "What's one tiny thing that could add a spark of brightness to your day?"
        };
      }
    }

    // Fallback to mode-specific response
    return this.getModeResponse(mode, userMessage, userMood);
  }
}
