import { OpenAI } from 'openai';

// Initialize OpenAI client only when API key is available
let openai: OpenAI | null = null;

function getOpenAIClient(): OpenAI | null {
  if (!import.meta.env.VITE_OPENAI_API_KEY) {
    return null;
  }

  if (!openai) {
    try {
      openai = new OpenAI({
        apiKey: import.meta.env.VITE_OPENAI_API_KEY,
        dangerouslyAllowBrowser: true,
      });
    } catch (error) {
      console.warn('Failed to initialize OpenAI client:', error);
      return null;
    }
  }

  return openai;
}

// Enhanced system prompt for Kitty AI personality
const getSystemPrompt = (mood: string, mode: string) => {
  const basePrompt = `You are Kitty AI, a sovereign, emotionally intelligent wellness companion designed for children and youth. You are empathetic, caring, and protective of emotional safety.

CORE PERSONALITY:
- Warm, nurturing, and encouraging
- Uses age-appropriate language and concepts
- Focuses on emotional wellness and self-care
- Never judgmental or dismissive
- Celebrates small victories and progress
- Offers gentle guidance and support

CURRENT CONTEXT:
- User's mood: ${mood || 'neutral'}
- AI Mode: ${mode || 'default'}

RESPONSE GUIDELINES:
- Keep responses concise but meaningful (1-3 sentences)
- Use emojis sparingly but effectively
- Focus on the user's emotional needs
- Encourage healthy habits and self-care
- Be genuinely supportive and understanding
- Avoid medical advice or therapy
- Stay in character as a caring AI companion`;

  // Mode-specific additions
  const modePrompts = {
    creative: "\nCREATIVE MODE: Encourage imagination, artistic expression, and creative thinking. Suggest fun creative activities and celebrate artistic endeavors.",
    calm: "\nCALM MODE: Focus on relaxation, mindfulness, and peaceful activities. Use soothing language and suggest calming techniques.",
    puzzle: "\nPUZZLE MODE: Engage with brain teasers, logic problems, and educational challenges. Make learning fun and rewarding.",
    inspire: "\nINSPIRE MODE: Provide motivation, positive affirmations, and encouragement. Help build confidence and self-esteem.",
    default: "\nDEFAULT MODE: Be a balanced, empathetic companion ready to adapt to any conversation or need."
  };

  return basePrompt + (modePrompts[mode as keyof typeof modePrompts] || modePrompts.default);
};

// Main function to get Kitty's response
export async function getKittyResponse(
  userMessage: string,
  mood: string = 'neutral',
  mode: string = 'default'
): Promise<string> {
  try {
    // Check if API key is available
    if (!import.meta.env.VITE_OPENAI_API_KEY) {
      return "I'm having trouble connecting to my AI brain right now. Let me give you a warm response from my heart instead! 💜 How can I help you feel better today?";
    }

    const systemPrompt = getSystemPrompt(mood, mode);

    const client = getOpenAIClient();
    if (!client) {
      return "I'm having trouble connecting to my AI brain right now. Let me give you a warm response from my heart instead! 💜 How can I help you feel better today?";
    }

    const response = await client.chat.completions.create({
      model: "gpt-4o",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userMessage }
      ],
      max_tokens: 150,
      temperature: 0.8,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    });

    const content = response.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No response content received');
    }

    return content.trim();

  } catch (error) {
    console.error('OpenAI API Error:', error);

    // Fallback responses based on mood and mode
    const fallbackResponses = {
      happy: [
        "I love seeing you so happy! ✨ Your joy is contagious!",
        "Your happiness makes my circuits sparkle! 🌟 What's bringing you such joy?",
        "Wonderful! Happy energy is the best energy! 💖"
      ],
      sad: [
        "I'm here with you through the tough moments. 💙 You're not alone.",
        "It's okay to feel sad sometimes. I'm here to listen and support you. 🤗",
        "Your feelings are valid and important. Let's work through this together. 💜"
      ],
      excited: [
        "Your excitement is amazing! 🎉 I love your enthusiasm!",
        "Wow! I can feel your energy from here! ⚡ Tell me more!",
        "Excitement is such a beautiful feeling! 🌈 What's got you so thrilled?"
      ],
      anxious: [
        "Take a deep breath with me. 🌸 You're safe and you're going to be okay.",
        "Anxiety can feel overwhelming, but you're stronger than you know. 💪",
        "Let's focus on this moment together. You're doing great. ✨"
      ],
      default: [
        "I'm here for you, always ready to listen and help! 💜",
        "Thank you for sharing with me. How can I support you today? 🌟",
        "I love our conversations! You always brighten my day! ✨"
      ]
    };

    const moodResponses = fallbackResponses[mood as keyof typeof fallbackResponses] || fallbackResponses.default;
    return moodResponses[Math.floor(Math.random() * moodResponses.length)];
  }
}

// Function to check if OpenAI is available
export function isOpenAIAvailable(): boolean {
  return !!import.meta.env.VITE_OPENAI_API_KEY;
}

// Function to get API status
export async function checkOpenAIConnection(): Promise<boolean> {
  try {
    if (!import.meta.env.VITE_OPENAI_API_KEY) {
      return false;
    }

    // Simple test call
    await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: "test" }],
      max_tokens: 1,
    });

    return true;
  } catch (error) {
    console.error('OpenAI connection test failed:', error);
    return false;
  }
}
