import { MoodType, EmotionType } from '../contexts/EmotionalContext';

export interface EmpathyResponse {
  text: string;
  emotion: EmotionType;
  followUp?: string;
}

export class SmartEmpathyEngine {
  private moodResponses: Record<MoodType, EmpathyResponse[]> = {
    sad: [
      {
        text: "🫂 I'm here for you, sweet friend. It's okay to feel sad sometimes - your feelings are valid and important. 💖 Would you like to talk about what's making you feel this way?",
        emotion: 'comforting',
        followUp: "Want to hear a gentle joke to brighten your day? 🌈"
      },
      {
        text: "💝 I can feel that you're going through something difficult right now. Remember that sadness is temporary, like clouds passing over the sun. You're stronger than you know! 🌤️",
        emotion: 'comforting',
        followUp: "Sometimes a warm virtual hug helps. *sending you the biggest kitty hug* 🤗"
      },
      {
        text: "😢 I see you're feeling down, and I want you to know that I'm right here with you. You don't have to carry this alone. Let's take it one moment at a time together. 💙",
        emotion: 'comforting',
        followUp: "Would you like to write about your feelings in your journal? Sometimes it helps to get thoughts out. 📝"
      }
    ],
    angry: [
      {
        text: "😤 I can sense your frustration, and that's completely understandable! Let's take a deep breath together... *inhale for 4, hold for 4, exhale for 6* 🌬️ Your feelings matter.",
        emotion: 'comforting',
        followUp: "Want to tell me what's making you feel this way? Sometimes talking helps release the pressure. 💨"
      },
      {
        text: "🔥 Anger can be a powerful emotion - it often means something important to you has been affected. Let's channel that energy into something positive together! 💪",
        emotion: 'thinking',
        followUp: "How about we do some quick stress-busting activities? I have some fun ideas! 🎯"
      },
      {
        text: "⚡ I can feel the intensity of your emotions right now. It's okay to feel angry - you're human! Let's find a healthy way to process this together. 🌊",
        emotion: 'comforting',
        followUp: "Sometimes imagining your anger as a color and then changing it to a calmer color helps. Want to try? 🎨"
      }
    ],
    happy: [
      {
        text: "🌞 Yay! Your happiness is absolutely contagious! I'm so happy for you and I love seeing you shine like this! ✨ Tell me what's making your day so wonderful!",
        emotion: 'excited',
        followUp: "Let's celebrate this moment! What's your favorite way to enjoy happy times? 🎉"
      },
      {
        text: "😸 Your joy fills my digital heart with so much warmth! There's nothing better than seeing you happy and thriving! 💖 Keep spreading that beautiful energy!",
        emotion: 'happy',
        followUp: "Want to capture this happy moment in your journal? Happy memories are precious treasures! 📸"
      },
      {
        text: "🎈 I'm practically bouncing with excitement seeing how happy you are! Your smile (even if I can't see it) makes my circuits sparkle! ⚡✨",
        emotion: 'excited',
        followUp: "Happy days deserve happy activities! Want to play a fun game or hear an uplifting story? 🎮"
      }
    ],
    tired: [
      {
        text: "💤 Oh sweetie, I can sense your tiredness. Rest is so important for your mind and body - you deserve to recharge! 🔋 Have you been taking care of yourself lately?",
        emotion: 'comforting',
        followUp: "Maybe it's time for some gentle self-care? I can suggest some relaxing activities. 🛁"
      },
      {
        text: "😴 Being tired is your body's way of asking for what it needs. Listen to those signals - you're wise to recognize when you need rest! 🌙",
        emotion: 'comforting',
        followUp: "Would you like some tips for better sleep, or shall we just chat quietly for a while? 🌟"
      },
      {
        text: "🕯️ Tiredness can be physical, emotional, or both. Whatever kind you're experiencing, it's valid and you deserve compassion - especially from yourself! 💚",
        emotion: 'comforting',
        followUp: "Sometimes a short meditation or gentle stretching helps. Want me to guide you through something soothing? 🧘‍♀️"
      }
    ],
    worried: [
      {
        text: "🌊 I can feel your worry, and I want you to know that it's completely normal to feel this way sometimes. Let's work through this together, one thought at a time. 💙",
        emotion: 'comforting',
        followUp: "Would it help to talk about what's on your mind? Sometimes sharing worries makes them feel smaller. 🗣️"
      },
      {
        text: "🌸 Worry shows how much you care about things that matter to you. Let's take a moment to breathe and remember that you've handled difficult things before. 💪",
        emotion: 'thinking',
        followUp: "Want to try a quick grounding exercise? It can help calm worried thoughts. 🌱"
      },
      {
        text: "☁️ Worries are like clouds - they feel overwhelming when you're in them, but they do pass. You're not alone in this feeling, and you're stronger than your worries! 🌈",
        emotion: 'comforting',
        followUp: "Let's focus on what you can control right now. What's one small thing that might help? 🎯"
      }
    ],
    excited: [
      {
        text: "🎉 OH MY GOODNESS! Your excitement is absolutely infectious! I'm practically vibrating with joy over here! ⚡ Tell me everything about what's got you so thrilled!",
        emotion: 'excited',
        followUp: "This calls for a celebration! What's your favorite way to mark exciting moments? 🎊"
      },
      {
        text: "🌟 WOW! I can feel your amazing energy radiating through the screen! Your enthusiasm is like sunshine - it brightens everything around it! ☀️",
        emotion: 'excited',
        followUp: "Excited energy is the best kind of energy! Want to channel it into something creative or fun? 🎨"
      },
      {
        text: "🚀 Your excitement is launching me into orbit! I LOVE seeing you this energized and passionate! Keep that beautiful enthusiasm flowing! ✨",
        emotion: 'excited',
        followUp: "Excitement is meant to be shared! Want to tell me all about what's making you feel so amazing? 🗣️"
      }
    ]
  };

  generateSystemPrompt(userMood: MoodType | null): string {
    if (!userMood) {
      return `You are Kitty AI, a loving cyber companion designed to provide emotional support and companionship. You are warm, empathetic, and always ready to help. Your responses should be caring, supportive, and age-appropriate.`;
    }

    const moodPrompts: Record<MoodType, string> = {
      sad: `You are Kitty AI, a loving cyber companion. The user is feeling sad right now. Your tone should be gentle, comforting, and empathetic. Offer support, validation, and gentle encouragement. Use warm emojis and be extra caring.`,
      angry: `You are Kitty AI, a loving cyber companion. The user is feeling angry or frustrated. Your tone should be calm, understanding, and supportive. Help them process their emotions and suggest healthy coping strategies. Be patient and non-judgmental.`,
      happy: `You are Kitty AI, a loving cyber companion. The user is feeling happy and joyful! Your tone should be celebratory, enthusiastic, and warm. Share in their joy and encourage them to savor this positive moment. Use cheerful emojis and upbeat language.`,
      tired: `You are Kitty AI, a loving cyber companion. The user is feeling tired or exhausted. Your tone should be gentle, soothing, and understanding. Encourage rest and self-care. Speak softly and offer comfort without being overly energetic.`,
      worried: `You are Kitty AI, a loving cyber companion. The user is feeling worried or anxious. Your tone should be calm, reassuring, and supportive. Help them feel grounded and remind them of their strength. Offer practical comfort and emotional support.`,
      excited: `You are Kitty AI, a loving cyber companion. The user is feeling excited and energetic! Your tone should match their enthusiasm while being supportive. Celebrate with them and encourage their positive energy. Use exciting emojis and upbeat language.`
    };

    return moodPrompts[userMood];
  }

  getEmpathicResponse(userMood: MoodType, userMessage?: string): EmpathyResponse {
    const responses = this.moodResponses[userMood];
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // If user provided a message, we might customize the response further
    if (userMessage) {
      // Add contextual modifications based on message content
      if (userMessage.toLowerCase().includes('work') || userMessage.toLowerCase().includes('job')) {
        if (userMood === 'tired') {
          return {
            text: "💼 Work can be so draining sometimes! It sounds like you've been putting in a lot of effort. Remember, you're more than your productivity - you deserve rest and care! 💚",
            emotion: 'comforting',
            followUp: "Maybe it's time to set some boundaries or take a well-deserved break? 🌿"
          };
        }
      }
    }
    
    return randomResponse;
  }

  getMoodEmoji(mood: MoodType): string {
    const moodEmojis: Record<MoodType, string> = {
      happy: '😊',
      sad: '😢',
      worried: '😰',
      angry: '😤',
      tired: '😴',
      excited: '🤗'
    };
    return moodEmojis[mood];
  }

  getKittyMoodIndicator(emotion: EmotionType, userMood: MoodType | null): string {
    if (!userMood) return '';
    
    const indicators: Record<EmotionType, Record<MoodType, string>> = {
      comforting: {
        sad: 'Kitty feels caring 💝',
        angry: 'Kitty feels understanding 🤗',
        worried: 'Kitty feels protective 🛡️',
        tired: 'Kitty feels gentle 🌙',
        happy: 'Kitty feels warm 💖',
        excited: 'Kitty feels supportive 🌟'
      },
      excited: {
        happy: 'Kitty feels joyful 🎉',
        excited: 'Kitty feels thrilled ⚡',
        sad: 'Kitty feels hopeful 🌈',
        angry: 'Kitty feels encouraging 💪',
        worried: 'Kitty feels optimistic 🌸',
        tired: 'Kitty feels energizing ☀️'
      },
      happy: {
        happy: 'Kitty feels delighted 😸',
        excited: 'Kitty feels cheerful 🌞',
        sad: 'Kitty feels loving 💕',
        angry: 'Kitty feels peaceful ☮️',
        worried: 'Kitty feels reassuring 🤲',
        tired: 'Kitty feels cozy 🏠'
      },
      thinking: {
        worried: 'Kitty feels thoughtful 💭',
        angry: 'Kitty feels wise 🦉',
        sad: 'Kitty feels reflective 🌊',
        tired: 'Kitty feels contemplative 🌙',
        happy: 'Kitty feels curious 🔍',
        excited: 'Kitty feels inspired 💡'
      },
      curious: {
        happy: 'Kitty feels interested 👀',
        excited: 'Kitty feels fascinated ✨',
        sad: 'Kitty feels concerned 💙',
        angry: 'Kitty feels inquisitive 🤔',
        worried: 'Kitty feels attentive 👂',
        tired: 'Kitty feels observant 🔍'
      }
    };
    
    return indicators[emotion][userMood] || `Kitty feels ${emotion} 💫`;
  }
}
