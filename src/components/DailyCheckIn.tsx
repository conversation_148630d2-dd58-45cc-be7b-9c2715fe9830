import React, { useState, useEffect } from 'react';
import { Calendar, Gift, Heart } from 'lucide-react';

interface DailyCheckInProps {
  onCheckIn: () => void;
}

const DailyCheckIn: React.FC<DailyCheckInProps> = ({ onCheckIn }) => {
  const [hasCheckedInToday, setHasCheckedInToday] = useState(false);
  const [streak, setStreak] = useState(0);
  const [showReward, setShowReward] = useState(false);

  useEffect(() => {
    const today = new Date().toDateString();
    const lastCheckIn = localStorage.getItem('kitty-last-checkin');
    const currentStreak = parseInt(localStorage.getItem('kitty-streak') || '0', 10);
    
    setHasCheckedInToday(lastCheckIn === today);
    setStreak(currentStreak);
  }, []);

  const handleCheckIn = () => {
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();
    const lastCheckIn = localStorage.getItem('kitty-last-checkin');
    
    let newStreak = streak;
    
    if (lastCheckIn === yesterday) {
      newStreak = streak + 1;
    } else if (lastCheckIn !== today) {
      newStreak = 1;
    }
    
    localStorage.setItem('kitty-last-checkin', today);
    localStorage.setItem('kitty-streak', newStreak.toString());
    
    setHasCheckedInToday(true);
    setStreak(newStreak);
    setShowReward(true);
    onCheckIn();
    
    setTimeout(() => setShowReward(false), 3000);
  };

  if (hasCheckedInToday) {
    return (
      <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Heart className="w-5 h-5 text-red-500 fill-red-400" />
          <span className="text-sm font-semibold text-green-600">Daily Check-in Complete!</span>
        </div>
        <p className="text-xs text-gray-600">
          🔥 {streak} day streak! Come back tomorrow for more rewards!
        </p>
      </div>
    );
  }

  return (
    <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg">
      <div className="text-center mb-3">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Calendar className="w-5 h-5 text-kitty-neon-purple" />
          <h3 className="text-sm font-semibold text-kitty-neon-purple">Daily Check-in</h3>
        </div>
        <p className="text-xs text-gray-600 mb-3">
          How are you feeling today? Let Kitty know you're here! 
          {streak > 0 && ` 🔥 ${streak} day streak!`}
        </p>
      </div>
      
      <button
        onClick={handleCheckIn}
        className="w-full bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white rounded-xl py-3 px-4 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
      >
        <div className="flex items-center justify-center gap-2">
          <Gift className="w-4 h-4" />
          <span className="text-sm font-medium">Check In & Earn Rewards!</span>
        </div>
      </button>
      
      {showReward && (
        <div className="mt-3 text-center animate-scale-in">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white rounded-lg py-2 px-3">
            <span className="text-sm font-bold">🎉 +2 Kitty Stars! Welcome back!</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default DailyCheckIn;
