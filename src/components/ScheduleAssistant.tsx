import React, { useState } from 'react';
import { Calendar, X, Clock, Bell, <PERSON>Off, Settings } from 'lucide-react';
import { useSchedule } from '../contexts/ScheduleContext';

const ScheduleAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { 
    reminders, 
    isScheduleEnabled, 
    nextReminder, 
    toggleSchedule, 
    toggleReminder,
    getActiveReminders 
  } = useSchedule();

  const activeReminders = getActiveReminders();
  const getTimeUntilNext = () => {
    if (!nextReminder) return null;
    
    const now = new Date();
    const [hours, minutes] = nextReminder.time.split(':').map(Number);
    const reminderTime = new Date();
    reminderTime.setHours(hours, minutes, 0, 0);
    
    if (reminderTime <= now) {
      reminderTime.setDate(reminderTime.getDate() + 1);
    }
    
    const diff = reminderTime.getTime() - now.getTime();
    const hoursUntil = Math.floor(diff / (1000 * 60 * 60));
    const minutesUntil = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hoursUntil > 0) {
      return `${hoursUntil}h ${minutesUntil}m`;
    }
    return `${minutesUntil}m`;
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-36 right-4 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full p-4 shadow-lg hover:scale-110 transition-all duration-300 z-20 group"
        title="Kitty Schedule Assistant"
      >
        <div className="relative">
          <Calendar className="w-6 h-6" />
          {isScheduleEnabled && activeReminders.length > 0 && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
          )}
          {nextReminder && (
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Next: {getTimeUntilNext()}
            </div>
          )}
        </div>
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-30 p-4">
      <div className="chat-container rounded-3xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Calendar className="w-6 h-6 text-kitty-neon-purple" />
            <h2 className="text-xl font-bold text-kitty-neon-purple">Kitty Schedule Assistant</h2>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 text-2xl transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Schedule Toggle */}
        <div className="mb-6 p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-800">Daily Reminders</h3>
              <p className="text-sm text-gray-600">
                {isScheduleEnabled 
                  ? `${activeReminders.length} active reminders helping you stay healthy!`
                  : 'Reminders are currently disabled'
                }
              </p>
            </div>
            <button
              onClick={toggleSchedule}
              className={`p-3 rounded-full transition-all duration-300 ${
                isScheduleEnabled
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-300 text-gray-600 hover:bg-gray-400'
              }`}
            >
              {isScheduleEnabled ? <Bell className="w-5 h-5" /> : <BellOff className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Next Reminder */}
        {isScheduleEnabled && nextReminder && (
          <div className="mb-6 p-4 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl">
            <div className="flex items-center gap-3">
              <div className="text-2xl">{nextReminder.icon}</div>
              <div>
                <h3 className="font-semibold text-gray-800">Next Reminder</h3>
                <p className="text-sm text-gray-600">{nextReminder.title}</p>
                <div className="flex items-center gap-1 mt-1">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-xs text-gray-500">
                    {nextReminder.time} • in {getTimeUntilNext()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Reminders List */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-kitty-neon-purple mb-3 flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Manage Reminders
          </h3>
          
          {reminders.map((reminder) => (
            <div
              key={reminder.id}
              className={`p-4 rounded-2xl transition-all duration-300 ${
                reminder.isActive && isScheduleEnabled
                  ? 'bg-white/70 border border-green-200'
                  : 'bg-gray-100 border border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`text-2xl ${reminder.isActive ? '' : 'opacity-50'}`}>
                    {reminder.icon}
                  </div>
                  <div>
                    <h4 className={`font-semibold ${reminder.isActive ? 'text-gray-800' : 'text-gray-500'}`}>
                      {reminder.title}
                    </h4>
                    <p className={`text-sm ${reminder.isActive ? 'text-gray-600' : 'text-gray-400'}`}>
                      {reminder.message}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-400">{reminder.time}</span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => toggleReminder(reminder.id)}
                  className={`p-2 rounded-full transition-all duration-300 ${
                    reminder.isActive
                      ? 'bg-green-500 text-white hover:bg-green-600'
                      : 'bg-gray-300 text-gray-600 hover:bg-gray-400'
                  }`}
                  title={reminder.isActive ? 'Disable reminder' : 'Enable reminder'}
                >
                  {reminder.isActive ? <Bell className="w-4 h-4" /> : <BellOff className="w-4 h-4" />}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Tips */}
        <div className="mt-6 p-4 bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl">
          <h4 className="font-semibold text-gray-800 mb-2">💡 Wellness Tips</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Stay hydrated throughout the day for better energy</li>
            <li>• Take regular breaks to prevent fatigue</li>
            <li>• Gentle stretching improves circulation and mood</li>
            <li>• Mindful moments help reduce stress and anxiety</li>
          </ul>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Kitty cares about your wellbeing! 💝
          </p>
        </div>
      </div>
    </div>
  );
};

export default ScheduleAssistant;
