import React from 'react';
import { Droplets, Activity, Target, Brain, Coffee, Moon } from 'lucide-react';

interface QuickActionsProps {
  onActionTaken: (action: string, message: string) => void;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onActionTaken }) => {
  const actions = [
    {
      id: 'hydrate',
      icon: Droplets,
      label: 'Drink Water',
      color: 'from-blue-400 to-blue-500',
      message: "Just had some water! 💧 Staying hydrated and feeling great!"
    },
    {
      id: 'stretch',
      icon: Activity,
      label: 'Stretch',
      color: 'from-green-400 to-green-500',
      message: "Did some stretching! 🧘‍♀️ My body feels so much better now!"
    },
    {
      id: 'play',
      icon: Target,
      label: 'Play Time',
      color: 'from-purple-400 to-purple-500',
      message: "Had some fun playtime! 🎮 It's important to enjoy life!"
    },
    {
      id: 'mindful',
      icon: Brain,
      label: 'Mindful Moment',
      color: 'from-pink-400 to-pink-500',
      message: "Took a mindful moment! 🌸 Feeling centered and peaceful."
    },
    {
      id: 'break',
      icon: Coffee,
      label: 'Take Break',
      color: 'from-orange-400 to-orange-500',
      message: "Taking a well-deserved break! ☕ Rest is so important!"
    },
    {
      id: 'rest',
      icon: Moon,
      label: 'Rest Time',
      color: 'from-indigo-400 to-indigo-500',
      message: "Time for some rest! 🌙 My body and mind need this recharge."
    }
  ];

  const handleActionClick = (action: typeof actions[0]) => {
    onActionTaken(action.id, action.message);
  };

  return (
    <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg">
      <h3 className="text-lg font-semibold text-kitty-neon-purple mb-4 text-center">
        Quick Wellness Actions ⚡
      </h3>
      
      <div className="grid grid-cols-3 gap-3">
        {actions.map((action) => {
          const IconComponent = action.icon;
          return (
            <button
              key={action.id}
              onClick={() => handleActionClick(action)}
              className={`p-3 rounded-xl bg-gradient-to-br ${action.color} text-white transition-all duration-300 hover:scale-105 hover:shadow-lg group`}
            >
              <div className="flex flex-col items-center gap-2">
                <IconComponent className="w-6 h-6 group-hover:animate-bounce-soft" />
                <span className="text-xs font-medium text-center leading-tight">
                  {action.label}
                </span>
              </div>
            </button>
          );
        })}
      </div>
      
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          Track your wellness activities with one tap! 🌟
        </p>
      </div>
    </div>
  );
};

export default QuickActions;
