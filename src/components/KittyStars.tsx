import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';

interface KittyStarsProps {
  onStarEarned?: () => void;
}

const KittyStars: React.FC<KittyStarsProps> = ({ onStarEarned }) => {
  const [stars, setStars] = useState(0);
  const [showStarAnimation, setShowStarAnimation] = useState(false);
  const [recentStars, setRecentStars] = useState<Array<{ id: number; x: number; y: number }>>([]);

  // Load stars from localStorage
  useEffect(() => {
    const savedStars = localStorage.getItem('kitty-stars');
    if (savedStars) {
      setStars(parseInt(savedStars, 10));
    }
  }, []);

  // Save stars to localStorage
  useEffect(() => {
    localStorage.setItem('kitty-stars', stars.toString());
  }, [stars]);

  const earnStar = () => {
    setStars(prev => prev + 1);
    setShowStarAnimation(true);
    onStarEarned?.();

    // Create floating star animation
    const newStar = {
      id: Date.now(),
      x: Math.random() * 200 + 50,
      y: Math.random() * 100 + 50,
    };
    setRecentStars(prev => [...prev, newStar]);

    // Remove animation after delay
    setTimeout(() => {
      setShowStarAnimation(false);
      setRecentStars(prev => prev.filter(star => star.id !== newStar.id));
    }, 2000);
  };

  // Auto-earn stars for positive interactions (demo purposes)
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance every 10 seconds
        earnStar();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative">
      {/* Stars Counter */}
      <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-full backdrop-blur-sm border border-yellow-400/30">
        <Star className="w-5 h-5 text-yellow-500 fill-yellow-400" />
        <span className="text-sm font-bold text-yellow-600">{stars}</span>
        <span className="text-xs text-gray-600">Kitty Stars</span>
      </div>

      {/* Star Animation */}
      {showStarAnimation && (
        <div className="absolute -top-2 -right-2 pointer-events-none">
          <div className="animate-bounce-soft">
            <Star className="w-6 h-6 text-yellow-500 fill-yellow-400 animate-spin" />
          </div>
        </div>
      )}

      {/* Floating Stars */}
      {recentStars.map((star) => (
        <div
          key={star.id}
          className="absolute pointer-events-none animate-sparkle-float"
          style={{
            left: `${star.x}%`,
            top: `${star.y}%`,
          }}
        >
          <Star className="w-4 h-4 text-yellow-500 fill-yellow-400" />
        </div>
      ))}

      {/* Achievement Milestones */}
      {stars > 0 && stars % 5 === 0 && (
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-center">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full animate-pulse-soft">
            🎉 {stars} Stars! Amazing!
          </div>
        </div>
      )}
    </div>
  );
};

export default KittyStars;
