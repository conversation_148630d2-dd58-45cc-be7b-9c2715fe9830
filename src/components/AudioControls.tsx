import React, { useState } from 'react';
import { Volume2, VolumeX, Music, Pause, Play } from 'lucide-react';
import { useAudio } from '../contexts/AudioContext';

const AudioControls: React.FC = () => {
  const { isPlaying, isMuted, volume, toggleMute, setVolume, stopTrack } = useAudio();
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
  };

  return (
    <div className="flex items-center gap-2">
      {/* Play/Pause Button */}
      <button
        onClick={isPlaying ? stopTrack : () => {}}
        className={`p-2 rounded-full transition-all duration-300 ${
          isPlaying 
            ? 'bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white hover:scale-110' 
            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
        }`}
        disabled={!isPlaying}
        title={isPlaying ? 'Stop ambient audio' : 'No audio playing'}
      >
        {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
      </button>

      {/* Volume Control */}
      <div className="relative">
        <button
          onClick={() => setShowVolumeSlider(!showVolumeSlider)}
          onMouseEnter={() => setShowVolumeSlider(true)}
          className={`p-2 rounded-full transition-all duration-300 ${
            isMuted 
              ? 'bg-red-500 text-white' 
              : 'bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white hover:scale-110'
          }`}
          title={isMuted ? 'Unmute audio' : 'Mute audio'}
        >
          {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
        </button>

        {/* Volume Slider */}
        {showVolumeSlider && (
          <div 
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-white/30"
            onMouseLeave={() => setShowVolumeSlider(false)}
          >
            <div className="flex flex-col items-center gap-2">
              <span className="text-xs text-gray-600 font-medium">Volume</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                style={{
                  background: `linear-gradient(to right, #FF69B4 0%, #FF69B4 ${volume * 100}%, #e5e7eb ${volume * 100}%, #e5e7eb 100%)`
                }}
              />
              <span className="text-xs text-gray-500">{Math.round(volume * 100)}%</span>
            </div>
          </div>
        )}
      </div>

      {/* Mute Toggle */}
      <button
        onClick={toggleMute}
        className={`p-2 rounded-full transition-all duration-300 ${
          isMuted 
            ? 'bg-red-500 text-white animate-pulse' 
            : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
        }`}
        title={isMuted ? 'Audio is muted' : 'Mute all audio'}
      >
        <Music className="w-4 h-4" />
      </button>

      {/* Audio Status Indicator */}
      {isPlaying && (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-500">Playing</span>
        </div>
      )}
    </div>
  );
};

export default AudioControls;
