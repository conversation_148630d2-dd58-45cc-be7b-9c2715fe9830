import React, { useState, useEffect } from 'react';
import { Heart, Droplets, Activity, Brain, Target, TrendingUp } from 'lucide-react';

interface WellnessStats {
  hydrationGoal: number;
  hydrationCount: number;
  stretchCount: number;
  playCount: number;
  mindfulnessCount: number;
  lastReset: string;
}

const WellnessDashboard: React.FC = () => {
  const [stats, setStats] = useState<WellnessStats>({
    hydrationGoal: 8,
    hydrationCount: 0,
    stretchCount: 0,
    playCount: 0,
    mindfulnessCount: 0,
    lastReset: new Date().toDateString()
  });

  // Load saved wellness stats
  useEffect(() => {
    const savedStats = localStorage.getItem('kitty-wellness-stats');
    if (savedStats) {
      try {
        const parsed = JSON.parse(savedStats);
        const today = new Date().toDateString();
        
        // Reset stats if it's a new day
        if (parsed.lastReset !== today) {
          setStats(prev => ({
            ...prev,
            hydrationCount: 0,
            stretchCount: 0,
            playCount: 0,
            mindfulnessCount: 0,
            lastReset: today
          }));
        } else {
          setStats(parsed);
        }
      } catch (error) {
        console.error('Error loading wellness stats:', error);
      }
    }
  }, []);

  // Save wellness stats
  useEffect(() => {
    localStorage.setItem('kitty-wellness-stats', JSON.stringify(stats));
  }, [stats]);

  const incrementStat = (type: keyof WellnessStats) => {
    if (typeof stats[type] === 'number') {
      setStats(prev => ({
        ...prev,
        [type]: (prev[type] as number) + 1
      }));
    }
  };

  const getProgressPercentage = (current: number, goal: number) => {
    return Math.min((current / goal) * 100, 100);
  };

  const getWellnessScore = () => {
    const hydrationScore = Math.min(stats.hydrationCount / stats.hydrationGoal, 1) * 25;
    const stretchScore = Math.min(stats.stretchCount / 3, 1) * 25; // Goal: 3 stretches
    const playScore = Math.min(stats.playCount / 2, 1) * 25; // Goal: 2 play sessions
    const mindfulnessScore = Math.min(stats.mindfulnessCount / 2, 1) * 25; // Goal: 2 mindful moments
    
    return Math.round(hydrationScore + stretchScore + playScore + mindfulnessScore);
  };

  const wellnessScore = getWellnessScore();

  return (
    <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-kitty-neon-purple flex items-center gap-2">
          <Heart className="w-5 h-5" />
          Daily Wellness
        </h3>
        <div className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4 text-green-500" />
          <span className="text-sm font-bold text-green-600">{wellnessScore}%</span>
        </div>
      </div>

      {/* Wellness Score Ring */}
      <div className="flex justify-center mb-4">
        <div className="relative w-20 h-20">
          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
            <path
              className="text-gray-200"
              stroke="currentColor"
              strokeWidth="3"
              fill="none"
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            <path
              className="text-green-500"
              stroke="currentColor"
              strokeWidth="3"
              fill="none"
              strokeDasharray={`${wellnessScore}, 100`}
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-bold text-gray-700">{wellnessScore}%</span>
          </div>
        </div>
      </div>

      {/* Wellness Stats */}
      <div className="grid grid-cols-2 gap-3">
        {/* Hydration */}
        <div className="bg-blue-50 rounded-xl p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Droplets className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Water</span>
            </div>
            <button
              onClick={() => incrementStat('hydrationCount')}
              className="text-xs bg-blue-500 text-white px-2 py-1 rounded-full hover:bg-blue-600 transition-colors"
            >
              +1
            </button>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-blue-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage(stats.hydrationCount, stats.hydrationGoal)}%` }}
              />
            </div>
            <span className="text-xs text-blue-600 font-medium">
              {stats.hydrationCount}/{stats.hydrationGoal}
            </span>
          </div>
        </div>

        {/* Stretch */}
        <div className="bg-green-50 rounded-xl p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium text-green-700">Stretch</span>
            </div>
            <button
              onClick={() => incrementStat('stretchCount')}
              className="text-xs bg-green-500 text-white px-2 py-1 rounded-full hover:bg-green-600 transition-colors"
            >
              +1
            </button>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-green-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage(stats.stretchCount, 3)}%` }}
              />
            </div>
            <span className="text-xs text-green-600 font-medium">
              {stats.stretchCount}/3
            </span>
          </div>
        </div>

        {/* Play */}
        <div className="bg-purple-50 rounded-xl p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium text-purple-700">Play</span>
            </div>
            <button
              onClick={() => incrementStat('playCount')}
              className="text-xs bg-purple-500 text-white px-2 py-1 rounded-full hover:bg-purple-600 transition-colors"
            >
              +1
            </button>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-purple-200 rounded-full h-2">
              <div 
                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage(stats.playCount, 2)}%` }}
              />
            </div>
            <span className="text-xs text-purple-600 font-medium">
              {stats.playCount}/2
            </span>
          </div>
        </div>

        {/* Mindfulness */}
        <div className="bg-pink-50 rounded-xl p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Brain className="w-4 h-4 text-pink-500" />
              <span className="text-sm font-medium text-pink-700">Mindful</span>
            </div>
            <button
              onClick={() => incrementStat('mindfulnessCount')}
              className="text-xs bg-pink-500 text-white px-2 py-1 rounded-full hover:bg-pink-600 transition-colors"
            >
              +1
            </button>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-pink-200 rounded-full h-2">
              <div 
                className="bg-pink-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage(stats.mindfulnessCount, 2)}%` }}
              />
            </div>
            <span className="text-xs text-pink-600 font-medium">
              {stats.mindfulnessCount}/2
            </span>
          </div>
        </div>
      </div>

      {/* Encouragement Message */}
      <div className="mt-4 text-center">
        {wellnessScore === 100 ? (
          <p className="text-sm text-green-600 font-medium">🎉 Perfect wellness day! You're amazing!</p>
        ) : wellnessScore >= 75 ? (
          <p className="text-sm text-blue-600 font-medium">🌟 Great job! You're doing wonderful!</p>
        ) : wellnessScore >= 50 ? (
          <p className="text-sm text-purple-600 font-medium">💪 Keep going! You're on the right track!</p>
        ) : (
          <p className="text-sm text-gray-600 font-medium">🌱 Every small step counts! You've got this!</p>
        )}
      </div>
    </div>
  );
};

export default WellnessDashboard;
