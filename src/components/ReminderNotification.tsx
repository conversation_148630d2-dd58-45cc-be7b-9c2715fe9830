import React, { useState, useEffect } from 'react';
import { X, Check } from 'lucide-react';
import { useSchedule } from '../contexts/ScheduleContext';

interface ReminderNotificationProps {
  onReminderAction?: (reminderId: string, action: 'dismiss' | 'snooze') => void;
}

const ReminderNotification: React.FC<ReminderNotificationProps> = ({ onReminderAction }) => {
  const { checkForDueReminders, dismissReminder } = useSchedule();
  const [activeNotifications, setActiveNotifications] = useState<any[]>([]);

  // Check for due reminders every minute
  useEffect(() => {
    const checkReminders = () => {
      const dueReminders = checkForDueReminders();
      
      // Add new notifications for due reminders
      dueReminders.forEach(reminder => {
        if (!activeNotifications.find(n => n.id === reminder.id)) {
          setActiveNotifications(prev => [...prev, {
            ...reminder,
            notificationId: `${reminder.id}-${Date.now()}`,
            timestamp: new Date()
          }]);
        }
      });
    };

    // Check immediately
    checkReminders();

    // Then check every minute
    const interval = setInterval(checkReminders, 60000);

    return () => clearInterval(interval);
  }, [checkForDueReminders, activeNotifications]);

  const handleDismiss = (notificationId: string, reminderId: string) => {
    setActiveNotifications(prev => prev.filter(n => n.notificationId !== notificationId));
    dismissReminder(reminderId);
    onReminderAction?.(reminderId, 'dismiss');
  };

  const handleSnooze = (notificationId: string, reminderId: string) => {
    setActiveNotifications(prev => prev.filter(n => n.notificationId !== notificationId));
    onReminderAction?.(reminderId, 'snooze');
    
    // Re-show notification in 10 minutes
    setTimeout(() => {
      const reminder = activeNotifications.find(n => n.id === reminderId);
      if (reminder) {
        setActiveNotifications(prev => [...prev, {
          ...reminder,
          notificationId: `${reminderId}-${Date.now()}`,
          timestamp: new Date()
        }]);
      }
    }, 10 * 60 * 1000); // 10 minutes
  };

  if (activeNotifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3">
      {activeNotifications.map((notification) => (
        <div
          key={notification.notificationId}
          className="bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-2xl p-4 shadow-lg max-w-sm animate-scale-in"
        >
          <div className="flex items-start gap-3">
            <div className="text-2xl animate-bounce-soft">
              {notification.icon}
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-1">
                {notification.title}
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {notification.message}
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => handleDismiss(notification.notificationId, notification.id)}
                  className="flex items-center gap-1 px-3 py-1 bg-green-500 text-white rounded-full text-xs hover:bg-green-600 transition-colors"
                >
                  <Check className="w-3 h-3" />
                  Done
                </button>
                <button
                  onClick={() => handleSnooze(notification.notificationId, notification.id)}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded-full text-xs hover:bg-blue-600 transition-colors"
                >
                  Snooze 10m
                </button>
              </div>
            </div>
            <button
              onClick={() => handleDismiss(notification.notificationId, notification.id)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ReminderNotification;
