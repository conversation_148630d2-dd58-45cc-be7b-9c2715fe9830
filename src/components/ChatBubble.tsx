
import React from 'react';

interface ChatBubbleProps {
  message: string;
  isKitty: boolean;
  emotion?: string;
  timestamp?: Date;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message, isKitty, emotion, timestamp }) => {
  const bubbleClass = isKitty 
    ? "chat-bubble rounded-3xl rounded-bl-lg p-4 mr-auto max-w-xs md:max-w-md shadow-lg"
    : "bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white rounded-3xl rounded-br-lg p-4 ml-auto max-w-xs md:max-w-md shadow-lg";

  return (
    <div className={`flex ${isKitty ? 'justify-start' : 'justify-end'} mb-4`}>
      <div className={`${bubbleClass} animate-scale-in`}>
        {isKitty && emotion && (
          <div className="flex items-center mb-2">
            <span className="text-xs text-kitty-neon-purple font-medium">Kitty feels {emotion}</span>
            <span className="ml-2 text-sm">
              {emotion === 'happy' && '😸'}
              {emotion === 'curious' && '🤔'}
              {emotion === 'thinking' && '💭'}
              {emotion === 'excited' && '🌟'}
              {emotion === 'comforting' && '💝'}
            </span>
          </div>
        )}
        <p className={`text-sm md:text-base ${isKitty ? 'text-gray-700' : 'text-white'}`}>
          {message}
        </p>
        {timestamp && (
          <div className={`text-xs mt-2 ${isKitty ? 'text-gray-500' : 'text-white/70'}`}>
            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatBubble;
