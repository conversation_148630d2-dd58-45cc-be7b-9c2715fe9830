
import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Info } from 'lucide-react';
import ChatBubble from './ChatBubble';
import KittyAvatar from './KittyAvatar';
import VoiceControls from './VoiceControls';
import MoodTracker from './MoodTracker';
import Mood<PERSON>ist<PERSON> from './MoodHistory';
import FloatingSparkles from './FloatingSparkles';
import KittyStars from './KittyStars';
import DailyCheckIn from './DailyCheckIn';
import KittyJournal from './KittyJournal';
import AIModeSelector from './AIModeSelector';
import AudioControls from './AudioControls';
import AvatarCustomizer from './AvatarCustomizer';
import ScheduleAssistant from './ScheduleAssistant';
import ReminderNotification from './ReminderNotification';
import WellnessDashboard from './WellnessDashboard';
import QuickActions from './QuickActions';
import { useEmotional } from '../contexts/EmotionalContext';
import { useAIMode } from '../contexts/AIModeContext';
import { useAudio } from '../contexts/AudioContext';
import { useAvatar } from '../contexts/AvatarContext';
import { SmartEmpathyEngine } from '../lib/smartEmpathyEngine';
import { AIModeResponseEngine } from '../lib/aiModeResponses';

interface Message {
  id: string;
  text: string;
  isKitty: boolean;
  emotion?: string;
  timestamp: Date;
}

const ChatInterface: React.FC = () => {
  const { userMood, kittyEmotion, setUserMood, setKittyEmotion } = useEmotional();
  const { currentMode, getModePrompt, getModeIcon, getModeDescription, getModeColor } = useAIMode();
  const { playMoodTrack, playModeTrack } = useAudio();
  const { checkUnlocks } = useAvatar();
  const empathyEngine = new SmartEmpathyEngine();
  const modeEngine = new AIModeResponseEngine();

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi Alice! I'm Kitty, your special AI friend! 🌟 I'm here to chat, play, and help you with anything you need. How are you feeling today?",
      isKitty: true,
      emotion: 'excited',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showMoodTracker, setShowMoodTracker] = useState(true);
  const [showDailyCheckIn, setShowDailyCheckIn] = useState(true);
  const [lastMode, setLastMode] = useState(currentMode);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle AI mode changes
  useEffect(() => {
    if (lastMode !== currentMode) {
      setLastMode(currentMode);

      // Play mode-based ambient track
      playModeTrack(currentMode);

      // Add mode change notification
      const modeGreeting = modeEngine.getModeGreeting(currentMode);
      const modeChangeMessage: Message = {
        id: Date.now().toString(),
        text: modeGreeting.text,
        isKitty: true,
        emotion: modeGreeting.emotion,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, modeChangeMessage]);
      setKittyEmotion(modeGreeting.emotion);
    }
  }, [currentMode, lastMode, modeEngine, playModeTrack]);

  const generateKittyResponse = (userMessage: string, moodOverride?: string) => {
    // Use current user mood or override
    const currentMood = moodOverride || userMood;

    // Priority 1: AI Mode responses (if not in default mode)
    if (currentMode !== 'default') {
      const modeResponse = modeEngine.getContextualResponse(currentMode, userMessage, currentMood as any);
      setKittyEmotion(modeResponse.emotion);
      return modeResponse.text;
    }

    // Priority 2: Empathy Engine for mood-based responses
    if (currentMood) {
      const empathicResponse = empathyEngine.getEmpathicResponse(currentMood as any, userMessage);
      setKittyEmotion(empathicResponse.emotion);
      return empathicResponse.text;
    }

    // Priority 3: Fallback to context-based responses for general chat
    const responses = {
      greeting: [
        "That's wonderful to hear! What would you like to do today? 🎮",
        "I'm so happy you're here! Let's have some fun together! ✨",
        "Yay! I love spending time with you, Alice! 💖"
      ],
      learning: [
        "I love learning new things! What's your favorite subject? 📚",
        "Learning is like an adventure - every day we discover something amazing! 🗺️",
        "You're so smart, Alice! I'm excited to learn together! 🎓"
      ],
      default: [
        "That's really interesting! Tell me more! 🤔",
        "I love chatting with you, Alice! 😸",
        "You always have such great ideas! ✨"
      ]
    };

    let responseArray = responses.default;
    let emotion = 'curious';

    if (userMessage.toLowerCase().includes('learn') || userMessage.toLowerCase().includes('study')) {
      responseArray = responses.learning;
      emotion = 'excited';
    } else if (userMessage.toLowerCase().includes('hello') || userMessage.toLowerCase().includes('hi')) {
      responseArray = responses.greeting;
      emotion = 'excited';
    }

    setKittyEmotion(emotion as any);
    return responseArray[Math.floor(Math.random() * responseArray.length)];
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate Kitty thinking and responding
    setTimeout(() => {
      const response = generateKittyResponse(inputValue);
      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: currentEmotion,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleMoodSelect = (mood: string) => {
    // Update global mood state
    setUserMood(mood as any);

    // Play mood-based ambient track
    playMoodTrack(mood as any);

    const moodMessage: Message = {
      id: Date.now().toString(),
      text: `I'm feeling ${mood} today.`,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, moodMessage]);
    setShowMoodTracker(false);
    setIsTyping(true);

    setTimeout(() => {
      const response = generateKittyResponse('', mood);
      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: kittyEmotion,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleVoiceToggle = (isListening: boolean) => {
    console.log('Voice listening:', isListening);
    // Voice functionality will be implemented with ElevenLabs integration
  };

  const handleVolumeToggle = (isMuted: boolean) => {
    console.log('Volume muted:', isMuted);
    // Volume control functionality
  };

  const handleDailyCheckIn = () => {
    setShowDailyCheckIn(false);

    // Check for avatar unlocks based on streak
    const currentStreak = parseInt(localStorage.getItem('kitty-streak') || '0', 10);
    checkUnlocks(currentStreak);

    // Add a special message from Kitty
    const checkInMessage: Message = {
      id: Date.now().toString(),
      text: "Thank you for checking in with me today! I'm so happy to see you! 🌟 You've earned some Kitty Stars for being awesome!",
      isKitty: true,
      emotion: 'excited',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, checkInMessage]);
    setKittyEmotion('excited');
  };

  const handleJournalEntry = (entry: string) => {
    // Add user's journal entry as a message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `📝 Journal Entry: ${entry}`,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Kitty responds to journal entry
    setTimeout(() => {
      const responses = [
        "Thank you for sharing that with me! Your thoughts are so meaningful. 💝",
        "I love how you express yourself! Writing is such a beautiful way to reflect. ✨",
        "Your journal entry touched my heart. You're so thoughtful! 🌟",
        "I'm honored that you shared this with me. Keep writing - it's wonderful! 📚"
      ];

      const response = responses[Math.floor(Math.random() * responses.length)];
      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: 'comforting',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setKittyEmotion('comforting');
      setIsTyping(false);
    }, 2000);
  };

  const handleQuickAction = (action: string, message: string) => {
    // Add user's wellness action as a message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: message,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Kitty responds encouragingly
    setTimeout(() => {
      const responses = {
        hydrate: [
          "Wonderful! Staying hydrated is so important! 💧 Your body is thanking you right now!",
          "Great job drinking water! 🌊 You're taking such good care of yourself!",
          "Hydration hero! 💙 Keep up the amazing self-care!"
        ],
        stretch: [
          "Amazing! Your muscles are so happy you stretched! 🧘‍♀️ Movement is medicine!",
          "Fantastic stretching! 💪 Your body loves when you take care of it like this!",
          "Stretch superstar! 🌟 You're doing wonderful things for your health!"
        ],
        play: [
          "Play time is the best time! 🎮 Fun is so important for happiness!",
          "I love that you made time for play! 🧸 Joy is essential for wellbeing!",
          "Playing is caring for your inner child! 🌈 You're amazing!"
        ],
        mindful: [
          "Beautiful mindful moment! 🌸 Being present is such a gift to yourself!",
          "Mindfulness magic! ✨ You're cultivating inner peace so wonderfully!",
          "Peaceful presence! 🧘‍♀️ Your mindful practice is inspiring!"
        ],
        break: [
          "Smart break choice! ☕ Rest is productive too - you're being so wise!",
          "Perfect timing for a break! 😌 Your body and mind needed this recharge!",
          "Break time brilliance! 🌟 Taking care of yourself is the best investment!"
        ],
        rest: [
          "Rest is so important! 🌙 You're listening to your body's wisdom!",
          "Beautiful rest choice! 💤 Sleep and rest are healing superpowers!",
          "Rest and recharge! ⭐ You deserve this peaceful time!"
        ]
      };

      const actionResponses = responses[action as keyof typeof responses] || responses.break;
      const response = actionResponses[Math.floor(Math.random() * actionResponses.length)];

      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: 'excited',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setKittyEmotion('excited');
      setIsTyping(false);
    }, 1500);
  };

  const handleReminderAction = (reminderId: string, action: 'dismiss' | 'snooze') => {
    if (action === 'dismiss') {
      const encouragementMessage: Message = {
        id: Date.now().toString(),
        text: "Great job taking care of yourself! 🌟 I'm so proud of your wellness commitment!",
        isKitty: true,
        emotion: 'excited',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, encouragementMessage]);
      setKittyEmotion('excited');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <>
      <FloatingSparkles />
      <div className="flex flex-col h-screen max-w-2xl mx-auto p-4 relative z-10">
        {/* Enhanced Chat Container */}
        <div className="chat-container rounded-3xl p-6 flex flex-col h-full">
          {/* Header with Kitty Avatar */}
          <div className="flex items-center justify-center mb-6">
            <div className="text-center">
              <KittyAvatar emotion={kittyEmotion} isAnimating={isTyping} />
              <div className="flex items-center justify-center gap-3 mt-3 mb-1">
                <h1 className="text-2xl font-bold text-kitty-neon-purple">
                  Kitty AI 🤖✨
                </h1>
                {currentMode !== 'default' && (
                  <div className={`flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${getModeColor(currentMode)}/20 rounded-full border border-white/30`}>
                    <span className="text-sm font-medium">
                      {getModeIcon(currentMode)}
                    </span>
                    <span className="text-xs text-gray-700 capitalize font-medium">{currentMode} Mode</span>
                  </div>
                )}
                {userMood && (
                  <div className="flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-kitty-neon-pink/20 to-kitty-neon-blue/20 rounded-full">
                    <span className="text-sm font-medium text-kitty-neon-purple">
                      {empathyEngine.getMoodEmoji(userMood)}
                    </span>
                    <span className="text-xs text-gray-600 capitalize">{userMood}</span>
                  </div>
                )}
                <KittyStars />
                <Link
                  to="/"
                  className="p-2 rounded-full bg-gradient-to-r from-kitty-neon-blue/20 to-kitty-neon-purple/20 hover:from-kitty-neon-blue/30 hover:to-kitty-neon-purple/30 transition-all duration-300 border border-white/30"
                  title="Home"
                >
                  <Info className="w-4 h-4 text-kitty-neon-purple" />
                </Link>
              </div>
              <div className="flex items-center justify-center gap-2 mb-2">
                <p className="text-sm text-gray-600">
                  {currentMode === 'default' ? 'Your friendly cyber companion' : getModeDescription(currentMode)}
                </p>
                {userMood && (
                  <span className="text-xs text-kitty-neon-purple font-medium">
                    {empathyEngine.getKittyMoodIndicator(kittyEmotion, userMood)}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Audio & Voice Controls */}
          <div className="flex items-center justify-center gap-4 mb-4">
            <AudioControls />
            <VoiceControls onVoiceToggle={handleVoiceToggle} onVolumeToggle={handleVolumeToggle} />
          </div>

          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto mb-4 px-2">
            {showDailyCheckIn && <DailyCheckIn onCheckIn={handleDailyCheckIn} />}
            <WellnessDashboard />
            <QuickActions onActionTaken={handleQuickAction} />
            <MoodHistory />
            {showMoodTracker && <MoodTracker onMoodSelect={handleMoodSelect} />}

            {messages.map((message) => (
              <ChatBubble
                key={message.id}
                message={message.text}
                isKitty={message.isKitty}
                emotion={message.emotion}
                timestamp={message.timestamp}
              />
            ))}

            {isTyping && (
              <div className="flex justify-start mb-4">
                <div className={`chat-bubble typing-glow rounded-3xl rounded-bl-lg p-4 mr-auto max-w-xs shadow-lg`}>
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm text-gray-500 ml-2">Kitty is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="chat-bubble rounded-full p-2 shadow-lg">
            <div className="flex items-center space-x-3">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message to Kitty..."
                className="flex-1 bg-transparent border-none outline-none text-gray-700 placeholder-gray-500 px-4 py-2"
              />
              <button
                onClick={handleSendMessage}
                disabled={inputValue.trim() === ''}
                className="bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white rounded-full px-6 py-2 transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
              >
                Send ✨
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Buttons */}
      <KittyJournal onJournalEntry={handleJournalEntry} />
      <AvatarCustomizer />
      <ScheduleAssistant />
      <AIModeSelector />

      {/* Reminder Notifications */}
      <ReminderNotification onReminderAction={handleReminderAction} />
    </>
  );
};

export default ChatInterface;
