
import React, { useState, useRef, useEffect } from 'react';
import ChatB<PERSON>ble from './ChatBubble';
import KittyAvatar from './KittyAvatar';
import VoiceControls from './VoiceControls';
import MoodTracker from './MoodTracker';

interface Message {
  id: string;
  text: string;
  isKitty: boolean;
  emotion?: string;
  timestamp: Date;
}

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi <PERSON>! I'm <PERSON>, your special AI friend! 🌟 I'm here to chat, play, and help you with anything you need. How are you feeling today?",
      isKitty: true,
      emotion: 'excited',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [currentEmotion, setCurrentEmotion] = useState<'happy' | 'curious' | 'thinking' | 'excited' | 'comforting'>('happy');
  const [isTyping, setIsTyping] = useState(false);
  const [showMoodTracker, setShowMoodTracker] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateKittyResponse = (userMessage: string, userMood?: string) => {
    const responses = {
      greeting: [
        "That's wonderful to hear! What would you like to do today? 🎮",
        "I'm so happy you're here! Let's have some fun together! ✨",
        "Yay! I love spending time with you, Alice! 💖"
      ],
      sad: [
        "I understand you're feeling sad. That's okay - everyone feels sad sometimes. Would you like to talk about it? 🤗",
        "I'm here for you, Alice. Remember that you're amazing and this feeling will pass. 💝",
        "Let's take some deep breaths together. You're not alone! 🌸"
      ],
      worried: [
        "When I feel worried, I like to think about happy things. What makes you smile? 😊",
        "Worries are like clouds - they come and go. Let's find something fun to focus on! ☁️",
        "You're so brave for sharing your feelings with me. That takes courage! 🌟"
      ],
      learning: [
        "I love learning new things! What's your favorite subject? 📚",
        "Learning is like an adventure - every day we discover something amazing! 🗺️",
        "You're so smart, Alice! I'm excited to learn together! 🎓"
      ],
      default: [
        "That's really interesting! Tell me more! 🤔",
        "I love chatting with you, Alice! 😸",
        "You always have such great ideas! ✨"
      ]
    };

    let responseArray = responses.default;
    let emotion: 'happy' | 'curious' | 'thinking' | 'excited' | 'comforting' = 'happy';

    if (userMood === 'sad') {
      responseArray = responses.sad;
      emotion = 'comforting';
    } else if (userMood === 'worried') {
      responseArray = responses.worried;
      emotion = 'comforting';
    } else if (userMessage.toLowerCase().includes('learn') || userMessage.toLowerCase().includes('study')) {
      responseArray = responses.learning;
      emotion = 'excited';
    } else if (userMessage.toLowerCase().includes('hello') || userMessage.toLowerCase().includes('hi')) {
      responseArray = responses.greeting;
      emotion = 'excited';
    } else {
      emotion = 'curious';
    }

    setCurrentEmotion(emotion);
    return responseArray[Math.floor(Math.random() * responseArray.length)];
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate Kitty thinking and responding
    setTimeout(() => {
      const response = generateKittyResponse(inputValue);
      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: currentEmotion,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleMoodSelect = (mood: string) => {
    const moodMessage: Message = {
      id: Date.now().toString(),
      text: `I'm feeling ${mood} today.`,
      isKitty: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, moodMessage]);
    setShowMoodTracker(false);
    setIsTyping(true);

    setTimeout(() => {
      const response = generateKittyResponse('', mood);
      const kittyMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        isKitty: true,
        emotion: currentEmotion,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, kittyMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleVoiceToggle = (isListening: boolean) => {
    console.log('Voice listening:', isListening);
    // Voice functionality will be implemented with ElevenLabs integration
  };

  const handleVolumeToggle = (isMuted: boolean) => {
    console.log('Volume muted:', isMuted);
    // Volume control functionality
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-screen max-w-2xl mx-auto p-4">
      {/* Header with Kitty Avatar */}
      <div className="flex items-center justify-center mb-6">
        <div className="text-center">
          <KittyAvatar emotion={currentEmotion} isAnimating={isTyping} />
          <h1 className="text-2xl font-bold text-kitty-neon-purple mt-3 mb-1">
            Kitty AI 🤖✨
          </h1>
          <p className="text-sm text-gray-600">Your friendly cyber companion</p>
        </div>
      </div>

      {/* Voice Controls */}
      <VoiceControls onVoiceToggle={handleVoiceToggle} onVolumeToggle={handleVolumeToggle} />

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto mb-4 px-2">
        {showMoodTracker && <MoodTracker onMoodSelect={handleMoodSelect} />}
        
        {messages.map((message) => (
          <ChatBubble
            key={message.id}
            message={message.text}
            isKitty={message.isKitty}
            emotion={message.emotion}
            timestamp={message.timestamp}
          />
        ))}
        
        {isTyping && (
          <div className="flex justify-start mb-4">
            <div className="chat-bubble rounded-3xl rounded-bl-lg p-4 mr-auto max-w-xs shadow-lg">
              <div className="flex items-center space-x-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-kitty-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-500 ml-2">Kitty is thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="chat-bubble rounded-full p-2 shadow-lg">
        <div className="flex items-center space-x-3">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message to Kitty..."
            className="flex-1 bg-transparent border-none outline-none text-gray-700 placeholder-gray-500 px-4 py-2"
          />
          <button
            onClick={handleSendMessage}
            disabled={inputValue.trim() === ''}
            className="bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white rounded-full px-6 py-2 transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
          >
            Send ✨
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
