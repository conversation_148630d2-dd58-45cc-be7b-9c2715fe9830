import React, { useState } from 'react';
import { Book<PERSON><PERSON>, PenTool, Sparkles } from 'lucide-react';

interface KittyJournalProps {
  onJournalEntry: (entry: string) => void;
}

const KittyJournal: React.FC<KittyJournalProps> = ({ onJournalEntry }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [journalEntry, setJournalEntry] = useState('');
  const [showPrompts, setShowPrompts] = useState(true);

  const journalPrompts = [
    "What made you smile today? 😊",
    "Describe a moment when you felt proud of yourself 🌟",
    "What are you grateful for right now? 🙏",
    "If you could tell your past self one thing, what would it be? 💭",
    "What's something new you learned today? 📚",
    "How did you show kindness today? 💝"
  ];

  const handleSubmit = () => {
    if (journalEntry.trim()) {
      onJournalEntry(journalEntry);
      setJournalEntry('');
      setIsOpen(false);
      setShowPrompts(true);
    }
  };

  const handlePromptClick = (prompt: string) => {
    setJournalEntry(prompt + '\n\n');
    setShowPrompts(false);
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full p-4 shadow-lg hover:scale-110 transition-all duration-300 z-20"
      >
        <BookOpen className="w-6 h-6" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-30 p-4">
      <div className="chat-container rounded-3xl p-6 max-w-md w-full max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <PenTool className="w-5 h-5 text-kitty-neon-purple" />
            <h3 className="text-lg font-bold text-kitty-neon-purple">Kitty Journal</h3>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-4">
          Share your thoughts with Kitty! Writing helps process emotions and celebrate moments. ✨
        </p>

        {showPrompts && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-kitty-neon-purple mb-2">
              Need inspiration? Try one of these:
            </h4>
            <div className="space-y-2">
              {journalPrompts.slice(0, 3).map((prompt, index) => (
                <button
                  key={index}
                  onClick={() => handlePromptClick(prompt)}
                  className="w-full text-left p-2 bg-white/50 hover:bg-white/70 rounded-lg text-xs transition-all duration-200 hover:scale-105"
                >
                  {prompt}
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowPrompts(false)}
              className="text-xs text-kitty-neon-purple mt-2 hover:underline"
            >
              Or write freely →
            </button>
          </div>
        )}

        <textarea
          value={journalEntry}
          onChange={(e) => setJournalEntry(e.target.value)}
          placeholder="Dear Kitty, today I..."
          className="w-full h-32 p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-kitty-neon-purple/50 text-sm"
        />

        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setIsOpen(false)}
            className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors text-sm"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!journalEntry.trim()}
            className="flex-1 py-2 px-4 bg-gradient-to-r from-kitty-neon-pink to-kitty-neon-blue text-white rounded-lg hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex items-center justify-center gap-1"
          >
            <Sparkles className="w-4 h-4" />
            Share with Kitty
          </button>
        </div>
      </div>
    </div>
  );
};

export default KittyJournal;
