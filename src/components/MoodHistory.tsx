import React from 'react';
import { useEmotional } from '../contexts/EmotionalContext';

const MoodHistory: React.FC = () => {
  const { moodHistory, getWeeklyMoodSummary } = useEmotional();
  
  // Get last 7 days of mood data
  const last7Days = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (6 - i));
    return date.toDateString();
  });

  const getMoodForDate = (dateString: string) => {
    return moodHistory.find(entry => entry.date === dateString)?.mood;
  };

  const getMoodEmoji = (mood: string | undefined) => {
    if (!mood) return '⚪'; // Empty circle for no mood recorded
    
    const moodEmojis: Record<string, string> = {
      happy: '😊',
      sad: '😢',
      worried: '😰',
      angry: '😤',
      tired: '😴',
      excited: '🤗'
    };
    return moodEmojis[mood] || '💫';
  };

  const getDayLabel = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();
    
    if (dateString === today) return 'Today';
    if (dateString === yesterday) return 'Yesterday';
    
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  // Check if it's Sunday and show weekly summary
  const isWeeklySummaryDay = () => {
    return new Date().getDay() === 0; // Sunday
  };

  const weeklyStats = getWeeklyMoodSummary();

  return (
    <div className="mb-4">
      {/* Weekly Summary (shown on Sundays) */}
      {isWeeklySummaryDay() && weeklyStats.totalMoods > 0 && (
        <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg bg-gradient-to-r from-purple-100 to-pink-100">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-kitty-neon-purple mb-2">
              🌟 Weekly Mood Recap! 🌟
            </h3>
            <p className="text-sm text-gray-700 mb-2">
              You shared your feelings <span className="font-bold text-kitty-neon-purple">{weeklyStats.totalMoods}</span> times this week!
            </p>
            <p className="text-sm text-gray-700 mb-2">
              You felt happy or excited <span className="font-bold text-green-600">{weeklyStats.happyCount}</span> times. That's pawsome! 🐾
            </p>
            <p className="text-xs text-gray-600">
              Your most common mood was <span className="capitalize font-medium">{weeklyStats.dominantMood}</span> 
              {getMoodEmoji(weeklyStats.dominantMood)}
            </p>
          </div>
        </div>
      )}

      {/* 7-Day Mood History */}
      <div className="chat-bubble rounded-2xl p-4 shadow-lg">
        <h3 className="text-sm font-semibold text-kitty-neon-purple mb-3 text-center">
          Your Week at a Glance 📅
        </h3>
        <div className="flex justify-between items-center">
          {last7Days.map((dateString, index) => {
            const mood = getMoodForDate(dateString);
            const dayLabel = getDayLabel(dateString);
            const isToday = dateString === new Date().toDateString();
            
            return (
              <div key={dateString} className="flex flex-col items-center">
                <div className="text-xs text-gray-500 mb-1 font-medium">
                  {dayLabel}
                </div>
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-lg transition-all duration-300 ${
                    isToday 
                      ? 'ring-2 ring-kitty-neon-purple ring-offset-2 scale-110' 
                      : 'hover:scale-105'
                  } ${
                    mood 
                      ? 'bg-gradient-to-br from-white to-gray-50 shadow-md' 
                      : 'bg-gray-100 border-2 border-dashed border-gray-300'
                  }`}
                  title={mood ? `Felt ${mood} on ${dayLabel}` : `No mood recorded for ${dayLabel}`}
                >
                  {getMoodEmoji(mood)}
                </div>
                {mood && (
                  <div className="text-xs text-gray-400 mt-1 capitalize">
                    {mood}
                  </div>
                )}
              </div>
            );
          })}
        </div>
        
        {moodHistory.length === 0 && (
          <div className="text-center mt-3">
            <p className="text-xs text-gray-500">
              Start tracking your moods to see your emotional journey! 🌈
            </p>
          </div>
        )}
        
        {moodHistory.length > 0 && (
          <div className="text-center mt-3">
            <p className="text-xs text-gray-500">
              You've been sharing your feelings for {moodHistory.length} day{moodHistory.length !== 1 ? 's' : ''}! 💖
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MoodHistory;
