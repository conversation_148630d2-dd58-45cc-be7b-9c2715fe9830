import React, { useState } from 'react';
import { Palette, X, <PERSON>, Star } from 'lucide-react';
import { useAvatar } from '../contexts/AvatarContext';

const AvatarCustomizer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentSkin, availableSkins, setCurrentSkin } = useAvatar();

  const handleSkinSelect = (skinId: string) => {
    const skin = availableSkins.find(s => s.id === skinId);
    if (skin && skin.isUnlocked) {
      setCurrentSkin(skinId as any);
      setIsOpen(false);
    }
  };

  const unlockedSkins = availableSkins.filter(skin => skin.isUnlocked);
  const lockedSkins = availableSkins.filter(skin => !skin.isUnlocked);

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full p-4 shadow-lg hover:scale-110 transition-all duration-300 z-20"
        title="Customize Kitty's appearance"
      >
        <Palette className="w-6 h-6" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-30 p-4">
      <div className="chat-container rounded-3xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Palette className="w-6 h-6 text-kitty-neon-purple" />
            <h2 className="text-xl font-bold text-kitty-neon-purple">Kitty Customization</h2>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 text-2xl transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-6 text-center">
          Unlock new Kitty skins by maintaining daily check-in streaks! Each skin has unique emotions and personality.
        </p>

        {/* Current Skin Display */}
        <div className="mb-6 text-center">
          <div className="inline-block p-4 bg-gradient-to-r from-white/20 to-white/10 rounded-2xl border border-white/30">
            <div className="text-4xl mb-2">
              {availableSkins.find(s => s.id === currentSkin)?.emoji || '🐱'}
            </div>
            <h3 className="font-bold text-kitty-neon-purple">
              {availableSkins.find(s => s.id === currentSkin)?.name || 'Classic Kitty'}
            </h3>
            <p className="text-xs text-gray-600">Currently Active</p>
          </div>
        </div>

        {/* Unlocked Skins */}
        {unlockedSkins.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-kitty-neon-purple mb-3 flex items-center gap-2">
              <Star className="w-5 h-5" />
              Available Skins
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {unlockedSkins.map((skin) => (
                <button
                  key={skin.id}
                  onClick={() => handleSkinSelect(skin.id)}
                  className={`p-4 rounded-2xl transition-all duration-300 hover:scale-105 text-left ${
                    currentSkin === skin.id
                      ? `bg-gradient-to-r ${skin.gradient} text-white shadow-lg ring-2 ring-white ring-offset-2`
                      : 'bg-white/50 hover:bg-white/70 text-gray-700 hover:shadow-md'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`text-2xl ${currentSkin === skin.id ? 'animate-bounce-soft' : ''}`}>
                      {skin.emoji}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-bold text-sm">{skin.name}</h4>
                        {currentSkin === skin.id && (
                          <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                            Active
                          </span>
                        )}
                      </div>
                      <p className={`text-xs ${currentSkin === skin.id ? 'text-white/90' : 'text-gray-600'}`}>
                        {skin.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Locked Skins */}
        {lockedSkins.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-500 mb-3 flex items-center gap-2">
              <Lock className="w-5 h-5" />
              Locked Skins
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {lockedSkins.map((skin) => (
                <div
                  key={skin.id}
                  className="p-4 rounded-2xl bg-gray-100 text-gray-500 relative overflow-hidden"
                >
                  <div className="flex items-start gap-3">
                    <div className="text-2xl opacity-50">
                      {skin.emoji}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-bold text-sm">{skin.name}</h4>
                        <Lock className="w-3 h-3" />
                      </div>
                      <p className="text-xs text-gray-500 mb-2">
                        {skin.description}
                      </p>
                      <div className="flex items-center gap-1">
                        <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                          {skin.unlockRequirement} day streak
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Lock overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-200/50 pointer-events-none" />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Keep checking in daily to unlock more amazing Kitty skins! 🌟
          </p>
        </div>
      </div>
    </div>
  );
};

export default AvatarCustomizer;
