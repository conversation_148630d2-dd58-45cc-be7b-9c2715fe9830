import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { useAIMode, AIModeType } from '../contexts/AIModeContext';

const AIModeSelector: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentMode, setCurrentMode, getModeIcon, getModeDescription, getModeColor } = useAIMode();

  const modes: Array<{
    id: AIModeType;
    name: string;
    icon: string;
    description: string;
    color: string;
    features: string[];
  }> = [
    {
      id: 'default',
      name: 'Default',
      icon: '🐱',
      description: 'Your loving cyber companion',
      color: 'from-kitty-neon-pink to-kitty-neon-blue',
      features: ['Empathic responses', 'Mood awareness', 'General chat']
    },
    {
      id: 'creative',
      name: 'Creative',
      icon: '🎨',
      description: 'Unleash imagination and artistic expression',
      color: 'from-purple-500 to-pink-500',
      features: ['Story writing', 'Art projects', 'Creative prompts', 'Fantasy worlds']
    },
    {
      id: 'calm',
      name: '<PERSON><PERSON>',
      icon: '😌',
      description: 'Find peace with mindfulness and relaxation',
      color: 'from-blue-400 to-green-400',
      features: ['Breathing exercises', 'Meditation', 'Relaxation', 'Mindfulness']
    },
    {
      id: 'puzzle',
      name: 'Puzzle',
      icon: '🧩',
      description: 'Challenge your mind with fun brain teasers',
      color: 'from-orange-500 to-red-500',
      features: ['Riddles', 'Brain teasers', 'Word games', 'Logic puzzles']
    },
    {
      id: 'inspire',
      name: 'Inspire',
      icon: '💡',
      description: 'Get motivated with uplifting wisdom',
      color: 'from-yellow-400 to-orange-500',
      features: ['Daily quotes', 'Motivation', 'Goal setting', 'Positive thinking']
    }
  ];

  const handleModeSelect = (mode: AIModeType) => {
    setCurrentMode(mode);
    setIsOpen(false);
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-4 right-4 bg-gradient-to-r ${getModeColor(currentMode)} text-white rounded-full p-4 shadow-lg hover:scale-110 transition-all duration-300 z-20 group`}
        title={`Current mode: ${currentMode}`}
      >
        <div className="relative">
          <Settings className="w-6 h-6" />
          <div className="absolute -top-1 -right-1 text-lg">
            {getModeIcon(currentMode)}
          </div>
          {currentMode !== 'default' && (
            <div className="absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
          )}
        </div>
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-30 p-4">
      <div className="chat-container rounded-3xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Sparkles className="w-6 h-6 text-kitty-neon-purple" />
            <h2 className="text-xl font-bold text-kitty-neon-purple">AI Personality Modes</h2>
          </div>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 text-2xl transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-6 text-center">
          Choose how Kitty should interact with you! Each mode brings different skills and personality traits.
        </p>

        <div className="grid gap-4">
          {modes.map((mode) => (
            <button
              key={mode.id}
              onClick={() => handleModeSelect(mode.id)}
              className={`relative p-4 rounded-2xl transition-all duration-300 hover:scale-105 text-left ${
                currentMode === mode.id
                  ? `bg-gradient-to-r ${mode.color} text-white shadow-lg ring-2 ring-white ring-offset-2`
                  : 'bg-white/50 hover:bg-white/70 text-gray-700 hover:shadow-md'
              }`}
            >
              <div className="flex items-start gap-4">
                <div className={`text-3xl ${currentMode === mode.id ? 'animate-bounce-soft' : ''}`}>
                  {mode.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-bold text-lg">{mode.name}</h3>
                    {currentMode === mode.id && (
                      <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                        Active
                      </span>
                    )}
                  </div>
                  <p className={`text-sm mb-3 ${currentMode === mode.id ? 'text-white/90' : 'text-gray-600'}`}>
                    {mode.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {mode.features.map((feature, index) => (
                      <span
                        key={index}
                        className={`text-xs px-2 py-1 rounded-full ${
                          currentMode === mode.id
                            ? 'bg-white/20 text-white'
                            : 'bg-gray-200 text-gray-600'
                        }`}
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              
              {currentMode === mode.id && (
                <div className="absolute top-2 right-2">
                  <div className="w-3 h-3 bg-white rounded-full animate-pulse" />
                </div>
              )}
            </button>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Your selected mode will be remembered for future conversations! 💫
          </p>
        </div>
      </div>
    </div>
  );
};

export default AIModeSelector;
