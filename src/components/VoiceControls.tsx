
import React, { useState } from 'react';
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from 'lucide-react';

interface VoiceControlsProps {
  onVoiceToggle: (isListening: boolean) => void;
  onVolumeToggle: (isMuted: boolean) => void;
}

const VoiceControls: React.FC<VoiceControlsProps> = ({ onVoiceToggle, onVolumeToggle }) => {
  const [isListening, setIsListening] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  const handleVoiceToggle = () => {
    const newState = !isListening;
    setIsListening(newState);
    onVoiceToggle(newState);
  };

  const handleVolumeToggle = () => {
    const newState = !isMuted;
    setIsMuted(newState);
    onVolumeToggle(newState);
  };

  return (
    <div className="flex gap-3 justify-center mb-4">
      <button
        onClick={handleVoiceToggle}
        className={`p-4 rounded-full transition-all duration-300 hover:scale-110 shadow-lg ${
          isListening
            ? 'bg-gradient-to-br from-kitty-neon-green to-kitty-mint animate-pulse-soft neon-outline'
            : 'chat-bubble hover:kitty-glow'
        }`}
      >
        {isListening ? (
          <div className="flex items-center gap-2">
            <Mic className="w-6 h-6 text-white" />
            <div className="voice-wave">
              <div className="voice-wave-bar bg-white"></div>
              <div className="voice-wave-bar bg-white"></div>
              <div className="voice-wave-bar bg-white"></div>
              <div className="voice-wave-bar bg-white"></div>
              <div className="voice-wave-bar bg-white"></div>
            </div>
          </div>
        ) : (
          <MicOff className="w-6 h-6 text-kitty-neon-purple" />
        )}
      </button>

      <button
        onClick={handleVolumeToggle}
        className={`p-4 rounded-full transition-all duration-300 hover:scale-110 shadow-lg ${
          isMuted
            ? 'bg-gray-400'
            : 'chat-bubble hover:kitty-glow'
        }`}
      >
        {isMuted ? (
          <VolumeX className="w-6 h-6 text-white" />
        ) : (
          <Volume2 className="w-6 h-6 text-kitty-neon-purple" />
        )}
      </button>
    </div>
  );
};

export default VoiceControls;
