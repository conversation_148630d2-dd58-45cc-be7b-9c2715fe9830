
import React, { useState } from 'react';

interface MoodTrackerProps {
  onMoodSelect: (mood: string) => void;
}

const MoodTracker: React.FC<MoodTrackerProps> = ({ onMoodSelect }) => {
  const [selectedMood, setSelectedMood] = useState<string | null>(null);

  const moods = [
    { emoji: '😊', label: 'Happy', value: 'happy', tooltip: "Let's celebrate together!" },
    { emoji: '😔', label: 'Sad', value: 'sad', tooltip: "I'm here to comfort you" },
    { emoji: '😰', label: 'Worried', value: 'worried', tooltip: "Let's talk through it" },
    { emoji: '😡', label: 'Angry', value: 'angry', tooltip: "I understand your feelings" },
    { emoji: '😴', label: 'Tired', value: 'tired', tooltip: "Rest is important" },
    { emoji: '🤗', label: 'Excited', value: 'excited', tooltip: "Your energy is contagious!" }
  ];

  const handleMoodClick = (mood: any) => {
    setSelectedMood(mood.value);
    onMoodSelect(mood.value);
  };

  return (
    <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg">
      <h3 className="text-lg font-semibold text-kitty-neon-purple mb-3 text-center">
        How are you feeling today, Alice? 💕
      </h3>
      <div className="grid grid-cols-3 gap-3">
        {moods.map((mood, index) => (
          <button
            key={mood.value}
            onClick={() => handleMoodClick(mood)}
            className={`mood-button relative p-3 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg animate-scale-in ${
              selectedMood === mood.value
                ? 'bg-gradient-to-br from-kitty-neon-pink to-kitty-neon-blue text-white neon-outline'
                : 'bg-white/50 hover:bg-white/70 text-gray-700 hover:animate-shimmer'
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="mood-tooltip">{mood.tooltip}</div>
            <div className="text-2xl mb-1 transition-transform duration-200 hover:scale-125">{mood.emoji}</div>
            <div className="text-xs font-medium">{mood.label}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default MoodTracker;
