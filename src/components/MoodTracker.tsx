
import React, { useState } from 'react';

interface MoodTrackerProps {
  onMoodSelect: (mood: string) => void;
}

const MoodTracker: React.FC<MoodTrackerProps> = ({ onMoodSelect }) => {
  const [selectedMood, setSelectedMood] = useState<string | null>(null);

  const moods = [
    { emoji: '😊', label: 'Happy', value: 'happy' },
    { emoji: '😔', label: 'Sad', value: 'sad' },
    { emoji: '😰', label: 'Worried', value: 'worried' },
    { emoji: '😡', label: 'Angry', value: 'angry' },
    { emoji: '😴', label: 'Tired', value: 'tired' },
    { emoji: '🤗', label: 'Excited', value: 'excited' }
  ];

  const handleMoodClick = (mood: any) => {
    setSelectedMood(mood.value);
    onMoodSelect(mood.value);
  };

  return (
    <div className="chat-bubble rounded-2xl p-4 mb-4 shadow-lg">
      <h3 className="text-lg font-semibold text-kitty-neon-purple mb-3 text-center">
        How are you feeling today, <PERSON>? 💕
      </h3>
      <div className="grid grid-cols-3 gap-3">
        {moods.map((mood) => (
          <button
            key={mood.value}
            onClick={() => handleMoodClick(mood)}
            className={`p-3 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg ${
              selectedMood === mood.value 
                ? 'bg-gradient-to-br from-kitty-neon-pink to-kitty-neon-blue text-white neon-outline' 
                : 'bg-white/50 hover:bg-white/70 text-gray-700'
            }`}
          >
            <div className="text-2xl mb-1">{mood.emoji}</div>
            <div className="text-xs font-medium">{mood.label}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default MoodTracker;
