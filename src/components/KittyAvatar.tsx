
import React, { useState } from 'react';
import { useAvatar } from '../contexts/AvatarContext';

interface KittyAvatarProps {
  emotion: 'happy' | 'curious' | 'thinking' | 'excited' | 'comforting';
  isAnimating?: boolean;
}

const KittyAvatar: React.FC<KittyAvatarProps> = ({ emotion, isAnimating = false }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [sparkles, setSparkles] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const { currentSkin, getSkinEmoji, getSkinGradient, getSkinGlow } = useAvatar();

  const getEmotionStyles = () => {
    const baseStyles = "w-20 h-20 rounded-full flex items-center justify-center text-4xl transition-all duration-300 cursor-pointer";
    const skinGradient = getSkinGradient(currentSkin);

    switch (emotion) {
      case 'happy':
        return `${baseStyles} bg-gradient-to-br ${skinGradient} animate-float`;
      case 'curious':
        return `${baseStyles} bg-gradient-to-br ${skinGradient} animate-pulse-soft`;
      case 'thinking':
        return `${baseStyles} bg-gradient-to-br ${skinGradient}`;
      case 'excited':
        return `${baseStyles} bg-gradient-to-br ${skinGradient} animate-bounce-soft`;
      case 'comforting':
        return `${baseStyles} bg-gradient-to-br ${skinGradient} animate-glow`;
      default:
        return `${baseStyles} bg-gradient-to-br ${skinGradient}`;
    }
  };

  const getEmotionEmoji = () => {
    return getSkinEmoji(currentSkin, emotion);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    // Generate sparkles on hover
    const newSparkles = Array.from({ length: 5 }, (_, i) => ({
      id: Date.now() + i,
      x: Math.random() * 80 + 10,
      y: Math.random() * 80 + 10,
    }));
    setSparkles(newSparkles);

    // Clear sparkles after animation
    setTimeout(() => setSparkles([]), 2000);
  };

  return (
    <div className="relative">
      <div
        className={`${getEmotionStyles()} ${isHovered ? 'scale-110 kitty-glow' : ''} ${isAnimating ? 'animate-bounce-soft' : ''} neon-outline relative overflow-hidden`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={() => setIsHovered(false)}
      >
        {getEmotionEmoji()}

        {/* Skin-based glow ring */}
        <div
          className="absolute inset-0 rounded-full opacity-30 animate-pulse-soft"
          style={{
            background: `radial-gradient(circle, ${getSkinGlow(currentSkin)}, transparent)`
          }}
        />
      </div>

      {/* Floating sparkles on hover */}
      {sparkles.map((sparkle) => (
        <div
          key={sparkle.id}
          className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-sparkle-float pointer-events-none"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`,
          }}
        />
      ))}
    </div>
  );
};

export default KittyAvatar;
