
import React, { useState } from 'react';

interface KittyAvatarProps {
  emotion: 'happy' | 'curious' | 'thinking' | 'excited' | 'comforting';
  isAnimating?: boolean;
}

const KittyAvatar: React.FC<KittyAvatarProps> = ({ emotion, isAnimating = false }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [sparkles, setSparkles] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const getEmotionStyles = () => {
    const baseStyles = "w-20 h-20 rounded-full flex items-center justify-center text-4xl transition-all duration-300 cursor-pointer";

    switch (emotion) {
      case 'happy':
        return `${baseStyles} bg-gradient-to-br from-kitty-pink to-kitty-neon-pink animate-float`;
      case 'curious':
        return `${baseStyles} bg-gradient-to-br from-kitty-blue to-kitty-neon-blue animate-pulse-soft`;
      case 'thinking':
        return `${baseStyles} bg-gradient-to-br from-kitty-purple to-kitty-neon-purple`;
      case 'excited':
        return `${baseStyles} bg-gradient-to-br from-kitty-yellow to-kitty-neon-green animate-bounce-soft`;
      case 'comforting':
        return `${baseStyles} bg-gradient-to-br from-kitty-mint to-kitty-blue animate-glow`;
      default:
        return `${baseStyles} bg-gradient-to-br from-kitty-pink to-kitty-neon-pink`;
    }
  };

  const getEmotionEmoji = () => {
    switch (emotion) {
      case 'happy':
        return '😸';
      case 'curious':
        return '🤔';
      case 'thinking':
        return '💭';
      case 'excited':
        return '🌟';
      case 'comforting':
        return '💝';
      default:
        return '😸';
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    // Generate sparkles on hover
    const newSparkles = Array.from({ length: 5 }, (_, i) => ({
      id: Date.now() + i,
      x: Math.random() * 80 + 10,
      y: Math.random() * 80 + 10,
    }));
    setSparkles(newSparkles);

    // Clear sparkles after animation
    setTimeout(() => setSparkles([]), 2000);
  };

  return (
    <div className="relative">
      <div
        className={`${getEmotionStyles()} ${isHovered ? 'scale-110 kitty-glow' : ''} ${isAnimating ? 'animate-bounce-soft' : ''} neon-outline relative overflow-hidden`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={() => setIsHovered(false)}
      >
        {getEmotionEmoji()}

        {/* Mood-based glow ring */}
        <div className={`absolute inset-0 rounded-full opacity-30 ${
          emotion === 'happy' ? 'bg-gradient-to-r from-yellow-400 to-pink-400' :
          emotion === 'curious' ? 'bg-gradient-to-r from-blue-400 to-purple-400' :
          emotion === 'thinking' ? 'bg-gradient-to-r from-purple-400 to-indigo-400' :
          emotion === 'excited' ? 'bg-gradient-to-r from-green-400 to-yellow-400' :
          emotion === 'comforting' ? 'bg-gradient-to-r from-pink-400 to-blue-400' :
          'bg-gradient-to-r from-pink-400 to-purple-400'
        } animate-pulse-soft`} />
      </div>

      {/* Floating sparkles on hover */}
      {sparkles.map((sparkle) => (
        <div
          key={sparkle.id}
          className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-sparkle-float pointer-events-none"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`,
          }}
        />
      ))}
    </div>
  );
};

export default KittyAvatar;
