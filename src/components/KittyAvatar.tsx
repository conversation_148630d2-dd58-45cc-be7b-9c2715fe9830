
import React, { useState } from 'react';

interface KittyAvatarProps {
  emotion: 'happy' | 'curious' | 'thinking' | 'excited' | 'comforting';
  isAnimating?: boolean;
}

const KittyAvatar: React.FC<KittyAvatarProps> = ({ emotion, isAnimating = false }) => {
  const [isHovered, setIsHovered] = useState(false);

  const getEmotionStyles = () => {
    const baseStyles = "w-20 h-20 rounded-full flex items-center justify-center text-4xl transition-all duration-300 cursor-pointer";
    
    switch (emotion) {
      case 'happy':
        return `${baseStyles} bg-gradient-to-br from-kitty-pink to-kitty-neon-pink animate-float`;
      case 'curious':
        return `${baseStyles} bg-gradient-to-br from-kitty-blue to-kitty-neon-blue animate-pulse-soft`;
      case 'thinking':
        return `${baseStyles} bg-gradient-to-br from-kitty-purple to-kitty-neon-purple`;
      case 'excited':
        return `${baseStyles} bg-gradient-to-br from-kitty-yellow to-kitty-neon-green animate-bounce-soft`;
      case 'comforting':
        return `${baseStyles} bg-gradient-to-br from-kitty-mint to-kitty-blue animate-glow`;
      default:
        return `${baseStyles} bg-gradient-to-br from-kitty-pink to-kitty-neon-pink`;
    }
  };

  const getEmotionEmoji = () => {
    switch (emotion) {
      case 'happy':
        return '😸';
      case 'curious':
        return '🤔';
      case 'thinking':
        return '💭';
      case 'excited':
        return '🌟';
      case 'comforting':
        return '💝';
      default:
        return '😸';
    }
  };

  return (
    <div 
      className={`${getEmotionStyles()} ${isHovered ? 'scale-110 kitty-glow' : ''} ${isAnimating ? 'animate-bounce-soft' : ''} neon-outline`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {getEmotionEmoji()}
    </div>
  );
};

export default KittyAvatar;
