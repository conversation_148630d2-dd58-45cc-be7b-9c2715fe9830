import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { EmotionalProvider } from "./contexts/EmotionalContext";
import { AIModeProvider } from "./contexts/AIModeContext";
import { AudioProvider } from "./contexts/AudioContext";
import { AvatarProvider } from "./contexts/AvatarContext";
import { ScheduleProvider } from "./contexts/ScheduleContext";
import Welcome from "./pages/Welcome";
import KittyApp from "./pages/App";
import About from "./pages/About";
import PressKit from "./pages/PressKit";
import Privacy from "./pages/Privacy";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <EmotionalProvider>
      <AIModeProvider>
        <AudioProvider>
          <AvatarProvider>
            <ScheduleProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                  <Routes>
                    <Route path="/" element={<Welcome />} />
                    <Route path="/app" element={<KittyApp />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/press-kit" element={<PressKit />} />
                    <Route path="/privacy" element={<Privacy />} />
                    {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </BrowserRouter>
              </TooltipProvider>
            </ScheduleProvider>
          </AvatarProvider>
        </AudioProvider>
      </AIModeProvider>
    </EmotionalProvider>
  </QueryClientProvider>
);

export default App;
