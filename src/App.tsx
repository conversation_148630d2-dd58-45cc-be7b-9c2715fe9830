import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { EmotionalProvider } from "./contexts/EmotionalContext";
import { AIModeProvider } from "./contexts/AIModeContext";
import { AudioProvider } from "./contexts/AudioContext";
import { AvatarProvider } from "./contexts/AvatarContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <EmotionalProvider>
      <AIModeProvider>
        <AudioProvider>
          <AvatarProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </TooltipProvider>
          </AvatarProvider>
        </AudioProvider>
      </AIModeProvider>
    </EmotionalProvider>
  </QueryClientProvider>
);

export default App;
