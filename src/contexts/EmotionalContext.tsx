import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type MoodType = 'happy' | 'sad' | 'worried' | 'angry' | 'tired' | 'excited';
export type EmotionType = 'happy' | 'curious' | 'thinking' | 'excited' | 'comforting';

interface EmotionalState {
  userMood: MoodType | null;
  kittyEmotion: EmotionType;
  moodHistory: Array<{
    date: string;
    mood: MoodType;
    timestamp: Date;
  }>;
  setUserMood: (mood: MoodType) => void;
  setKittyEmotion: (emotion: EmotionType) => void;
  addMoodToHistory: (mood: MoodType) => void;
  getWeeklyMoodSummary: () => {
    totalMoods: number;
    happyCount: number;
    dominantMood: MoodType;
    moodCounts: Record<MoodType, number>;
  };
}

const EmotionalContext = createContext<EmotionalState | undefined>(undefined);

interface EmotionalProviderProps {
  children: ReactNode;
}

export const EmotionalProvider: React.FC<EmotionalProviderProps> = ({ children }) => {
  const [userMood, setUserMoodState] = useState<MoodType | null>(null);
  const [kittyEmotion, setKittyEmotionState] = useState<EmotionType>('happy');
  const [moodHistory, setMoodHistory] = useState<Array<{
    date: string;
    mood: MoodType;
    timestamp: Date;
  }>>([]);

  // Load mood history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('kitty-mood-history');
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        // Convert timestamp strings back to Date objects
        const historyWithDates = parsed.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        }));
        setMoodHistory(historyWithDates);
      } catch (error) {
        console.error('Error loading mood history:', error);
      }
    }
  }, []);

  // Save mood history to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('kitty-mood-history', JSON.stringify(moodHistory));
  }, [moodHistory]);

  const setUserMood = (mood: MoodType) => {
    setUserMoodState(mood);
    addMoodToHistory(mood);
  };

  const setKittyEmotion = (emotion: EmotionType) => {
    setKittyEmotionState(emotion);
  };

  const addMoodToHistory = (mood: MoodType) => {
    const today = new Date().toDateString();
    const newEntry = {
      date: today,
      mood,
      timestamp: new Date()
    };
    
    setMoodHistory(prev => {
      // Remove any existing entry for today and add the new one
      const filtered = prev.filter(entry => entry.date !== today);
      return [...filtered, newEntry].slice(-30); // Keep last 30 days
    });
  };

  const getWeeklyMoodSummary = () => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    const weeklyMoods = moodHistory.filter(entry => 
      entry.timestamp >= oneWeekAgo
    );
    
    const moodCounts: Record<MoodType, number> = {
      happy: 0,
      sad: 0,
      worried: 0,
      angry: 0,
      tired: 0,
      excited: 0
    };
    
    weeklyMoods.forEach(entry => {
      moodCounts[entry.mood]++;
    });
    
    const totalMoods = weeklyMoods.length;
    const happyCount = moodCounts.happy + moodCounts.excited;
    
    // Find dominant mood
    let dominantMood: MoodType = 'happy';
    let maxCount = 0;
    Object.entries(moodCounts).forEach(([mood, count]) => {
      if (count > maxCount) {
        maxCount = count;
        dominantMood = mood as MoodType;
      }
    });
    
    return {
      totalMoods,
      happyCount,
      dominantMood,
      moodCounts
    };
  };

  const value: EmotionalState = {
    userMood,
    kittyEmotion,
    moodHistory,
    setUserMood,
    setKittyEmotion,
    addMoodToHistory,
    getWeeklyMoodSummary
  };

  return (
    <EmotionalContext.Provider value={value}>
      {children}
    </EmotionalContext.Provider>
  );
};

export const useEmotional = () => {
  const context = useContext(EmotionalContext);
  if (context === undefined) {
    throw new Error('useEmotional must be used within an EmotionalProvider');
  }
  return context;
};
