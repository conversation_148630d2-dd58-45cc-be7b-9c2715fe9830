import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type AvatarSkinType = 'default' | 'calico' | 'neon' | 'robot' | 'galaxy' | 'rainbow';

interface AvatarSkin {
  id: AvatarSkinType;
  name: string;
  description: string;
  emoji: string;
  unlockRequirement: number; // Number of days streak required
  isUnlocked: boolean;
  gradient: string;
  glowColor: string;
}

interface AvatarState {
  currentSkin: AvatarSkinType;
  availableSkins: AvatarSkin[];
  setCurrentSkin: (skin: AvatarSkinType) => void;
  checkUnlocks: (streak: number) => void;
  getSkinEmoji: (skin: AvatarSkinType, emotion: string) => string;
  getSkinGradient: (skin: AvatarSkinType) => string;
  getSkinGlow: (skin: AvatarSkinType) => string;
}

const AvatarContext = createContext<AvatarState | undefined>(undefined);

interface AvatarProviderProps {
  children: ReactNode;
}

export const AvatarProvider: React.FC<AvatarProviderProps> = ({ children }) => {
  const [currentSkin, setCurrentSkinState] = useState<AvatarSkinType>('default');
  const [availableSkins, setAvailableSkins] = useState<AvatarSkin[]>([
    {
      id: 'default',
      name: 'Classic Kitty',
      description: 'Your original cyber companion',
      emoji: '🐱',
      unlockRequirement: 0,
      isUnlocked: true,
      gradient: 'from-pink-400 to-purple-400',
      glowColor: 'rgba(255, 105, 180, 0.4)'
    },
    {
      id: 'calico',
      name: 'Calico Cat',
      description: 'Warm and cozy with patches of color',
      emoji: '🐈',
      unlockRequirement: 3,
      isUnlocked: false,
      gradient: 'from-orange-400 to-amber-400',
      glowColor: 'rgba(251, 146, 60, 0.4)'
    },
    {
      id: 'neon',
      name: 'Neon Cyber',
      description: 'Electric and futuristic',
      emoji: '🤖',
      unlockRequirement: 7,
      isUnlocked: false,
      gradient: 'from-cyan-400 to-blue-400',
      glowColor: 'rgba(34, 211, 238, 0.4)'
    },
    {
      id: 'robot',
      name: 'Robo Kitty',
      description: 'Mechanical precision meets feline grace',
      emoji: '🦾',
      unlockRequirement: 14,
      isUnlocked: false,
      gradient: 'from-gray-400 to-slate-400',
      glowColor: 'rgba(148, 163, 184, 0.4)'
    },
    {
      id: 'galaxy',
      name: 'Galaxy Cat',
      description: 'Cosmic and mysterious',
      emoji: '🌌',
      unlockRequirement: 21,
      isUnlocked: false,
      gradient: 'from-purple-600 to-indigo-600',
      glowColor: 'rgba(124, 58, 237, 0.4)'
    },
    {
      id: 'rainbow',
      name: 'Rainbow Spirit',
      description: 'Pure joy and endless possibilities',
      emoji: '🌈',
      unlockRequirement: 30,
      isUnlocked: false,
      gradient: 'from-pink-500 via-purple-500 to-indigo-500',
      glowColor: 'rgba(236, 72, 153, 0.4)'
    }
  ]);

  // Load saved avatar preferences
  useEffect(() => {
    const savedSkin = localStorage.getItem('kitty-avatar-skin');
    const savedUnlocks = localStorage.getItem('kitty-avatar-unlocks');
    
    if (savedSkin && ['default', 'calico', 'neon', 'robot', 'galaxy', 'rainbow'].includes(savedSkin)) {
      setCurrentSkinState(savedSkin as AvatarSkinType);
    }
    
    if (savedUnlocks) {
      try {
        const unlocks = JSON.parse(savedUnlocks);
        setAvailableSkins(prev => prev.map(skin => ({
          ...skin,
          isUnlocked: unlocks[skin.id] || skin.id === 'default'
        })));
      } catch (error) {
        console.error('Error loading avatar unlocks:', error);
      }
    }
  }, []);

  // Save avatar preferences
  useEffect(() => {
    localStorage.setItem('kitty-avatar-skin', currentSkin);
  }, [currentSkin]);

  useEffect(() => {
    const unlocks = availableSkins.reduce((acc, skin) => {
      acc[skin.id] = skin.isUnlocked;
      return acc;
    }, {} as Record<string, boolean>);
    localStorage.setItem('kitty-avatar-unlocks', JSON.stringify(unlocks));
  }, [availableSkins]);

  const setCurrentSkin = (skin: AvatarSkinType) => {
    const skinData = availableSkins.find(s => s.id === skin);
    if (skinData && skinData.isUnlocked) {
      setCurrentSkinState(skin);
    }
  };

  const checkUnlocks = (streak: number) => {
    setAvailableSkins(prev => prev.map(skin => ({
      ...skin,
      isUnlocked: skin.isUnlocked || streak >= skin.unlockRequirement
    })));
  };

  const getSkinEmoji = (skin: AvatarSkinType, emotion: string): string => {
    const skinData = availableSkins.find(s => s.id === skin);
    if (!skinData) return '🐱';

    // Emotion-based variations for different skins
    const emotionMappings: Record<AvatarSkinType, Record<string, string>> = {
      default: {
        happy: '😸',
        sad: '😿',
        excited: '😻',
        curious: '🙀',
        thinking: '🤔',
        comforting: '😽'
      },
      calico: {
        happy: '🐈‍⬛',
        sad: '🐈',
        excited: '🐈‍⬛',
        curious: '🐈',
        thinking: '🐈‍⬛',
        comforting: '🐈'
      },
      neon: {
        happy: '🤖',
        sad: '🤖',
        excited: '⚡',
        curious: '🔍',
        thinking: '💭',
        comforting: '💙'
      },
      robot: {
        happy: '🦾',
        sad: '🔧',
        excited: '⚙️',
        curious: '🔬',
        thinking: '💻',
        comforting: '🛡️'
      },
      galaxy: {
        happy: '🌟',
        sad: '🌙',
        excited: '✨',
        curious: '🔭',
        thinking: '🌌',
        comforting: '🌠'
      },
      rainbow: {
        happy: '🌈',
        sad: '🌧️',
        excited: '🎆',
        curious: '🔮',
        thinking: '💫',
        comforting: '☀️'
      }
    };

    return emotionMappings[skin]?.[emotion] || skinData.emoji;
  };

  const getSkinGradient = (skin: AvatarSkinType): string => {
    const skinData = availableSkins.find(s => s.id === skin);
    return skinData?.gradient || 'from-pink-400 to-purple-400';
  };

  const getSkinGlow = (skin: AvatarSkinType): string => {
    const skinData = availableSkins.find(s => s.id === skin);
    return skinData?.glowColor || 'rgba(255, 105, 180, 0.4)';
  };

  const value: AvatarState = {
    currentSkin,
    availableSkins,
    setCurrentSkin,
    checkUnlocks,
    getSkinEmoji,
    getSkinGradient,
    getSkinGlow
  };

  return (
    <AvatarContext.Provider value={value}>
      {children}
    </AvatarContext.Provider>
  );
};

export const useAvatar = () => {
  const context = useContext(AvatarContext);
  if (context === undefined) {
    throw new Error('useAvatar must be used within an AvatarProvider');
  }
  return context;
};
