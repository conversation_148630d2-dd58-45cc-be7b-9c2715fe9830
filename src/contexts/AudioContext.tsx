import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Howl } from 'howler';
import { MoodType } from './EmotionalContext';
import { AIModeType } from './AIModeContext';

interface AudioState {
  isPlaying: boolean;
  isMuted: boolean;
  currentTrack: string | null;
  volume: number;
  playMoodTrack: (mood: MoodType) => void;
  playModeTrack: (mode: AIModeType) => void;
  stopTrack: () => void;
  toggleMute: () => void;
  setVolume: (volume: number) => void;
}

const AudioContext = createContext<AudioState | undefined>(undefined);

interface AudioProviderProps {
  children: ReactNode;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<string | null>(null);
  const [volume, setVolumeState] = useState(0.3);
  const [currentHowl, setCurrentHowl] = useState<Howl | null>(null);

  // Load saved audio preferences
  useEffect(() => {
    const savedMuted = localStorage.getItem('kitty-audio-muted');
    const savedVolume = localStorage.getItem('kitty-audio-volume');
    
    if (savedMuted) {
      setIsMuted(JSON.parse(savedMuted));
    }
    if (savedVolume) {
      setVolumeState(parseFloat(savedVolume));
    }
  }, []);

  // Save audio preferences
  useEffect(() => {
    localStorage.setItem('kitty-audio-muted', JSON.stringify(isMuted));
  }, [isMuted]);

  useEffect(() => {
    localStorage.setItem('kitty-audio-volume', volume.toString());
  }, [volume]);

  // Mood-based ambient tracks (using placeholder URLs - in production, these would be actual audio files)
  const moodTracks: Record<MoodType, string> = {
    happy: '/audio/happy-ambient.mp3',
    sad: '/audio/calm-rain.mp3',
    worried: '/audio/peaceful-waves.mp3',
    angry: '/audio/deep-breathing.mp3',
    tired: '/audio/sleepy-lullaby.mp3',
    excited: '/audio/energetic-chimes.mp3'
  };

  // Mode-based ambient tracks
  const modeTracks: Record<AIModeType, string> = {
    default: '/audio/gentle-ambient.mp3',
    creative: '/audio/inspiring-melody.mp3',
    calm: '/audio/meditation-bells.mp3',
    puzzle: '/audio/focus-tones.mp3',
    inspire: '/audio/uplifting-harmony.mp3'
  };

  const createHowl = (src: string): Howl => {
    return new Howl({
      src: [src],
      loop: true,
      volume: isMuted ? 0 : volume,
      html5: true,
      onload: () => {
        console.log(`Audio loaded: ${src}`);
      },
      onloaderror: (id, error) => {
        console.log(`Audio load error for ${src}:`, error);
        // Fallback to silence or default behavior
      },
      onplay: () => {
        setIsPlaying(true);
      },
      onstop: () => {
        setIsPlaying(false);
      },
      onend: () => {
        setIsPlaying(false);
      }
    });
  };

  const playTrack = (trackUrl: string, trackName: string) => {
    // Stop current track if playing
    if (currentHowl) {
      currentHowl.stop();
      currentHowl.unload();
    }

    // Don't play if muted or same track
    if (isMuted || currentTrack === trackName) {
      setCurrentTrack(null);
      setCurrentHowl(null);
      return;
    }

    try {
      const howl = createHowl(trackUrl);
      setCurrentHowl(howl);
      setCurrentTrack(trackName);
      howl.play();
    } catch (error) {
      console.log('Audio playback not available:', error);
      // Graceful fallback - continue without audio
    }
  };

  const playMoodTrack = (mood: MoodType) => {
    const trackUrl = moodTracks[mood];
    playTrack(trackUrl, `mood-${mood}`);
  };

  const playModeTrack = (mode: AIModeType) => {
    const trackUrl = modeTracks[mode];
    playTrack(trackUrl, `mode-${mode}`);
  };

  const stopTrack = () => {
    if (currentHowl) {
      currentHowl.stop();
      currentHowl.unload();
      setCurrentHowl(null);
      setCurrentTrack(null);
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    
    if (currentHowl) {
      currentHowl.volume(newMuted ? 0 : volume);
    }
  };

  const setVolume = (newVolume: number) => {
    setVolumeState(newVolume);
    
    if (currentHowl && !isMuted) {
      currentHowl.volume(newVolume);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentHowl) {
        currentHowl.stop();
        currentHowl.unload();
      }
    };
  }, [currentHowl]);

  const value: AudioState = {
    isPlaying,
    isMuted,
    currentTrack,
    volume,
    playMoodTrack,
    playModeTrack,
    stopTrack,
    toggleMute,
    setVolume
  };

  return (
    <AudioContext.Provider value={value}>
      {children}
    </AudioContext.Provider>
  );
};

export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};
