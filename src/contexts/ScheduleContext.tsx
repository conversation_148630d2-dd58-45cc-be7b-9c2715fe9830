import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type ReminderType = 'hydration' | 'play' | 'stretch' | 'break' | 'mindfulness';

interface Reminder {
  id: string;
  type: ReminderType;
  title: string;
  message: string;
  icon: string;
  time: string; // HH:MM format
  isActive: boolean;
  lastTriggered?: Date;
}

interface ScheduleState {
  reminders: <PERSON>minder[];
  isScheduleEnabled: boolean;
  nextReminder: Reminder | null;
  toggleSchedule: () => void;
  toggleReminder: (id: string) => void;
  dismissReminder: (id: string) => void;
  getActiveReminders: () => Reminder[];
  checkForDueReminders: () => Reminder[];
}

const ScheduleContext = createContext<ScheduleState | undefined>(undefined);

interface ScheduleProviderProps {
  children: ReactNode;
}

export const ScheduleProvider: React.FC<ScheduleProviderProps> = ({ children }) => {
  const [isScheduleEnabled, setIsScheduleEnabled] = useState(true);
  const [reminders, setReminders] = useState<Reminder[]>([
    {
      id: 'hydration-1',
      type: 'hydration',
      title: 'Hydration Time! 💧',
      message: "Time for a refreshing drink of water! Your body will thank you! 🌊",
      icon: '💧',
      time: '09:00',
      isActive: true
    },
    {
      id: 'hydration-2',
      type: 'hydration',
      title: 'Water Break! 💧',
      message: "Let's stay hydrated! A glass of water keeps you energized! ✨",
      icon: '💧',
      time: '12:00',
      isActive: true
    },
    {
      id: 'hydration-3',
      type: 'hydration',
      title: 'Afternoon Hydration! 💧',
      message: "Afternoon water break! Keep that energy flowing! 🌟",
      icon: '💧',
      time: '15:00',
      isActive: true
    },
    {
      id: 'play-1',
      type: 'play',
      title: 'Play Time! 🧸',
      message: "Time to have some fun! Play is important for your happiness! 🎮",
      icon: '🧸',
      time: '10:30',
      isActive: true
    },
    {
      id: 'play-2',
      type: 'play',
      title: 'Fun Break! 🎯',
      message: "Let's take a fun break! Do something that makes you smile! 😊",
      icon: '🎯',
      time: '16:30',
      isActive: true
    },
    {
      id: 'stretch-1',
      type: 'stretch',
      title: 'Stretch Time! 🧘‍♀️',
      message: "Time to stretch those muscles! Your body loves movement! 💪",
      icon: '🧘‍♀️',
      time: '11:00',
      isActive: true
    },
    {
      id: 'stretch-2',
      type: 'stretch',
      title: 'Movement Break! 🤸‍♀️',
      message: "Let's get moving! A little stretch goes a long way! 🌈",
      icon: '🤸‍♀️',
      time: '14:00',
      isActive: true
    },
    {
      id: 'mindfulness-1',
      type: 'mindfulness',
      title: 'Mindful Moment! 🌸',
      message: "Take a deep breath and be present. You're doing great! 🌺",
      icon: '🌸',
      time: '13:00',
      isActive: true
    },
    {
      id: 'break-1',
      type: 'break',
      title: 'Rest Break! 😌',
      message: "Time for a little rest! Even superheroes need breaks! ⭐",
      icon: '😌',
      time: '17:00',
      isActive: true
    }
  ]);

  // Load saved schedule preferences
  useEffect(() => {
    const savedEnabled = localStorage.getItem('kitty-schedule-enabled');
    const savedReminders = localStorage.getItem('kitty-schedule-reminders');
    
    if (savedEnabled !== null) {
      setIsScheduleEnabled(JSON.parse(savedEnabled));
    }
    
    if (savedReminders) {
      try {
        const parsed = JSON.parse(savedReminders);
        setReminders(prev => prev.map(reminder => {
          const saved = parsed.find((r: any) => r.id === reminder.id);
          return saved ? { ...reminder, isActive: saved.isActive, lastTriggered: saved.lastTriggered ? new Date(saved.lastTriggered) : undefined } : reminder;
        }));
      } catch (error) {
        console.error('Error loading schedule preferences:', error);
      }
    }
  }, []);

  // Save schedule preferences
  useEffect(() => {
    localStorage.setItem('kitty-schedule-enabled', JSON.stringify(isScheduleEnabled));
  }, [isScheduleEnabled]);

  useEffect(() => {
    const reminderData = reminders.map(r => ({
      id: r.id,
      isActive: r.isActive,
      lastTriggered: r.lastTriggered
    }));
    localStorage.setItem('kitty-schedule-reminders', JSON.stringify(reminderData));
  }, [reminders]);

  const toggleSchedule = () => {
    setIsScheduleEnabled(prev => !prev);
  };

  const toggleReminder = (id: string) => {
    setReminders(prev => prev.map(reminder => 
      reminder.id === id ? { ...reminder, isActive: !reminder.isActive } : reminder
    ));
  };

  const dismissReminder = (id: string) => {
    setReminders(prev => prev.map(reminder => 
      reminder.id === id ? { ...reminder, lastTriggered: new Date() } : reminder
    ));
  };

  const getActiveReminders = () => {
    return reminders.filter(reminder => reminder.isActive && isScheduleEnabled);
  };

  const checkForDueReminders = () => {
    if (!isScheduleEnabled) return [];
    
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    const today = now.toDateString();
    
    return reminders.filter(reminder => {
      if (!reminder.isActive) return false;
      
      // Check if reminder time matches current time (within 1 minute)
      const reminderHour = parseInt(reminder.time.split(':')[0]);
      const reminderMinute = parseInt(reminder.time.split(':')[1]);
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      
      const isTimeMatch = reminderHour === currentHour && Math.abs(reminderMinute - currentMinute) <= 1;
      
      // Check if already triggered today
      const lastTriggeredToday = reminder.lastTriggered && reminder.lastTriggered.toDateString() === today;
      
      return isTimeMatch && !lastTriggeredToday;
    });
  };

  const getNextReminder = () => {
    if (!isScheduleEnabled) return null;
    
    const now = new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    
    const activeReminders = getActiveReminders();
    const upcomingReminders = activeReminders
      .map(reminder => {
        const [hours, minutes] = reminder.time.split(':').map(Number);
        const reminderMinutes = hours * 60 + minutes;
        
        // If reminder is later today
        if (reminderMinutes > currentMinutes) {
          return { ...reminder, minutesUntil: reminderMinutes - currentMinutes };
        }
        // If reminder is tomorrow
        return { ...reminder, minutesUntil: (24 * 60) + reminderMinutes - currentMinutes };
      })
      .sort((a, b) => a.minutesUntil - b.minutesUntil);
    
    return upcomingReminders[0] || null;
  };

  const nextReminder = getNextReminder();

  const value: ScheduleState = {
    reminders,
    isScheduleEnabled,
    nextReminder,
    toggleSchedule,
    toggleReminder,
    dismissReminder,
    getActiveReminders,
    checkForDueReminders
  };

  return (
    <ScheduleContext.Provider value={value}>
      {children}
    </ScheduleContext.Provider>
  );
};

export const useSchedule = () => {
  const context = useContext(ScheduleContext);
  if (context === undefined) {
    throw new Error('useSchedule must be used within a ScheduleProvider');
  }
  return context;
};
