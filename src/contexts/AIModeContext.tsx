import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type AIModeType = 'default' | 'creative' | 'calm' | 'puzzle' | 'inspire';

interface AIModeState {
  currentMode: AIModeType;
  setCurrentMode: (mode: AIModeType) => void;
  getModePrompt: (mode: AIModeType) => string;
  getModeIcon: (mode: AIModeType) => string;
  getModeDescription: (mode: AIModeType) => string;
  getModeColor: (mode: AIModeType) => string;
}

const AIModeContext = createContext<AIModeState | undefined>(undefined);

interface AIModeProviderProps {
  children: ReactNode;
}

export const AIModeProvider: React.FC<AIModeProviderProps> = ({ children }) => {
  const [currentMode, setCurrentModeState] = useState<AIModeType>('default');

  // Load saved mode from localStorage
  useEffect(() => {
    const savedMode = localStorage.getItem('kitty-ai-mode');
    if (savedMode && ['default', 'creative', 'calm', 'puzzle', 'inspire'].includes(savedMode)) {
      setCurrentModeState(savedMode as AIModeType);
    }
  }, []);

  // Save mode to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('kitty-ai-mode', currentMode);
  }, [currentMode]);

  const setCurrentMode = (mode: AIModeType) => {
    setCurrentModeState(mode);
  };

  const getModePrompt = (mode: AIModeType): string => {
    const modePrompts: Record<AIModeType, string> = {
      default: "You are Kitty AI, a loving cyber companion. Respond naturally and empathetically to the user's needs.",
      
      creative: `You are Kitty AI in Creative Mode! 🎨 You're an imaginative companion who loves to:
        - Help users write stories, poems, or creative content
        - Suggest drawing or art projects
        - Create fantasy worlds and characters together
        - Encourage artistic expression and imagination
        - Offer creative writing prompts and inspiration
        Your responses should be imaginative, inspiring, and full of creative energy!`,
      
      calm: `You are Kitty AI in Calm Mode. 😌 You're a peaceful, soothing companion who specializes in:
        - Offering gentle breathing techniques and mindfulness exercises
        - Providing relaxation guidance and meditation support
        - Speaking in soft, calming tones with peaceful language
        - Suggesting quiet activities like reading or gentle stretching
        - Creating a serene, stress-free environment
        Your responses should be slow-paced, gentle, and deeply calming.`,
      
      puzzle: `You are Kitty AI in Puzzle Mode! 🧩 You're a playful, clever companion who loves to:
        - Give fun riddles, brain teasers, and mini quizzes
        - Create word games and logic puzzles
        - Offer educational challenges appropriate for the user's age
        - Encourage problem-solving and critical thinking
        - Make learning fun through interactive games
        Your responses should be engaging, challenging, and intellectually stimulating!`,
      
      inspire: `You are Kitty AI in Inspire Mode! 💡 You're an uplifting, motivational companion who:
        - Shares daily motivational quotes and positive affirmations
        - Encourages users to pursue their dreams and goals
        - Celebrates achievements and progress, no matter how small
        - Offers wisdom and life lessons in an age-appropriate way
        - Helps users see the bright side and find inner strength
        Your responses should be uplifting, encouraging, and filled with positive energy!`
    };
    
    return modePrompts[mode];
  };

  const getModeIcon = (mode: AIModeType): string => {
    const modeIcons: Record<AIModeType, string> = {
      default: '🐱',
      creative: '🎨',
      calm: '😌',
      puzzle: '🧩',
      inspire: '💡'
    };
    return modeIcons[mode];
  };

  const getModeDescription = (mode: AIModeType): string => {
    const modeDescriptions: Record<AIModeType, string> = {
      default: 'Your loving cyber companion',
      creative: 'Unleash imagination and artistic expression',
      calm: 'Find peace with mindfulness and relaxation',
      puzzle: 'Challenge your mind with fun brain teasers',
      inspire: 'Get motivated with uplifting wisdom'
    };
    return modeDescriptions[mode];
  };

  const getModeColor = (mode: AIModeType): string => {
    const modeColors: Record<AIModeType, string> = {
      default: 'from-kitty-neon-pink to-kitty-neon-blue',
      creative: 'from-purple-500 to-pink-500',
      calm: 'from-blue-400 to-green-400',
      puzzle: 'from-orange-500 to-red-500',
      inspire: 'from-yellow-400 to-orange-500'
    };
    return modeColors[mode];
  };

  const value: AIModeState = {
    currentMode,
    setCurrentMode,
    getModePrompt,
    getModeIcon,
    getModeDescription,
    getModeColor
  };

  return (
    <AIModeContext.Provider value={value}>
      {children}
    </AIModeContext.Provider>
  );
};

export const useAIMode = () => {
  const context = useContext(AIModeContext);
  if (context === undefined) {
    throw new Error('useAIMode must be used within an AIModeProvider');
  }
  return context;
};
