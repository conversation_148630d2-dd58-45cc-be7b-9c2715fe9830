import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Ghost, Sparkles } from 'lucide-react';
import KittyAvatar from '../components/KittyAvatar';
import FloatingSparkles from '../components/FloatingSparkles';

const NotFound: React.FC = () => {
  const location = useLocation();
  const [isAnimating, setIsAnimating] = useState(true);

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );

    const interval = setInterval(() => {
      setIsAnimating(prev => !prev);
    }, 3000);

    return () => clearInterval(interval);
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-kitty-gradient-start via-kitty-gradient-middle to-kitty-gradient-end relative overflow-hidden">
      <FloatingSparkles />

      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />

      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="text-center">

          {/* Ghost Kitty */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="opacity-70 transform hover:scale-110 transition-transform duration-300">
                <KittyAvatar emotion="sad" isAnimating={isAnimating} />
              </div>
              <div className="absolute -top-2 -right-2">
                <Ghost className="w-8 h-8 text-white/80 animate-bounce" />
              </div>
            </div>
          </div>

          {/* Error Message */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 mb-8">
            <h1 className="text-6xl md:text-8xl font-bold text-gray-800 mb-4">
              404
            </h1>
            <h2 className="text-2xl md:text-3xl font-bold text-purple-700 mb-4">
              👻 Page Not Found
            </h2>
            <p className="text-lg text-gray-600 mb-6 max-w-md mx-auto">
              Oops! This page seems to have vanished into the digital void.
              Even Kitty can't find it! 🐱
            </p>

            {/* Kitty's Message */}
            <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-2xl p-6 border border-purple-300 mb-6">
              <p className="text-gray-800 italic">
                "Don't worry, human! Let me guide you back to safety.
                The digital realm can be confusing, but I'm here to help! 💜"
              </p>
              <p className="text-sm text-purple-600 mt-2">- Kitty AI</p>
            </div>
          </div>

          {/* Navigation Options */}
          <div className="space-y-4">
            <Link
              to="/"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 text-lg font-bold"
            >
              <Home className="w-5 h-5" />
              Return Home
              <Sparkles className="w-5 h-5" />
            </Link>

            <div className="flex flex-wrap justify-center gap-4 mt-6">
              <Link
                to="/app"
                className="inline-flex items-center gap-2 px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
              >
                Launch Kitty AI
              </Link>

              <Link
                to="/about"
                className="inline-flex items-center gap-2 px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
              >
                About Kitty
              </Link>
            </div>
          </div>

          {/* Fun Error Codes */}
          <div className="mt-12">
            <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 inline-block">
              <p className="text-sm text-gray-600 mb-2">Error Code Classification:</p>
              <div className="flex flex-wrap justify-center gap-2 text-xs">
                <span className="bg-red-100 text-red-700 px-2 py-1 rounded">GHOST_PAGE</span>
                <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded">KITTY_CONFUSED</span>
                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">DIGITAL_VOID</span>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default NotFound;
