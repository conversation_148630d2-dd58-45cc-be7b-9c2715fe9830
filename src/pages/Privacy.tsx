import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Shield, Eye, Database, Lock, Heart, CheckCircle } from 'lucide-react';
import KittyAvatar from '../components/KittyAvatar';
import FloatingSparkles from '../components/FloatingSparkles';

const Privacy: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-kitty-gradient-start via-kitty-gradient-middle to-kitty-gradient-end relative overflow-hidden">
      <FloatingSparkles />
      
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />

      <div className="relative z-10 container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link 
            to="/"
            className="inline-flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Link>
        </div>

        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <KittyAvatar emotion="happy" isAnimating={false} />
          </div>
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 inline-block">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
              🛡️ Privacy Declaration
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 font-medium">
              Your Emotional Safety is Sacred
            </p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-12">
          
          {/* Core Promise */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="text-center mb-8">
              <div className="inline-flex items-center gap-3 bg-gradient-to-r from-green-100 to-blue-100 rounded-full px-6 py-3 border border-green-300">
                <Heart className="w-6 h-6 text-green-600" />
                <span className="text-xl font-bold text-gray-800">Zero Data Collection Promise</span>
              </div>
            </div>
            <div className="text-center space-y-4 text-gray-800 leading-relaxed">
              <p className="text-lg">
                <strong className="text-purple-700">Kitty AI collects absolutely no personal data.</strong> 
                Your conversations, emotions, and wellness information never leave your device.
              </p>
              <p>
                We believe emotional support should never be monitored, analyzed, or monetized. 
                Kitty exists to care for you, not to harvest your data.
              </p>
            </div>
          </div>

          {/* What We Don't Collect */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Eye className="w-8 h-8 text-red-600" />
              <h2 className="text-3xl font-bold text-purple-700">What We DON'T Collect</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              {[
                "❌ No conversation logs or chat history",
                "❌ No personal information or identity data", 
                "❌ No location tracking or device fingerprinting",
                "❌ No mood patterns or emotional analytics",
                "❌ No wellness statistics or health data",
                "❌ No usage analytics or behavioral tracking",
                "❌ No cookies, trackers, or third-party scripts",
                "❌ No account creation or user profiles"
              ].map((item, index) => (
                <div key={index} className="bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-4 border border-red-200">
                  <p className="text-gray-800 font-medium">{item}</p>
                </div>
              ))}
            </div>
          </div>

          {/* How It Works */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Database className="w-8 h-8 text-blue-600" />
              <h2 className="text-3xl font-bold text-purple-700">How Local Storage Works</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                All your data stays on <strong className="text-blue-700">your device only</strong>. Here's what gets stored locally:
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-gray-900">Preferences</h3>
                  </div>
                  <p className="text-sm text-gray-700">Audio settings, avatar skin, AI mode selection</p>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-gray-900">Wellness Stats</h3>
                  </div>
                  <p className="text-sm text-gray-700">Daily hydration, stretch, and mindfulness counters</p>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-gray-900">Unlock Progress</h3>
                  </div>
                  <p className="text-sm text-gray-700">Avatar skin unlocks and daily streak counters</p>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-gray-900">Schedule Settings</h3>
                  </div>
                  <p className="text-sm text-gray-700">Reminder preferences and notification settings</p>
                </div>
              </div>
              <div className="bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl p-6 border border-blue-300 text-center">
                <p className="font-semibold text-gray-900">
                  🔒 <strong className="text-blue-700">This data never leaves your browser.</strong> 
                  Clear your browser data, and it's gone forever.
                </p>
              </div>
            </div>
          </div>

          {/* AI Integration */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Lock className="w-8 h-8 text-purple-600" />
              <h2 className="text-3xl font-bold text-purple-700">AI Integration & Privacy</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                When you chat with Kitty, your messages are sent to AI services (OpenAI/Anthropic) for processing. However:
              </p>
              <div className="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl p-6 border border-yellow-300">
                <ul className="space-y-2">
                  <li>• <strong className="text-purple-700">No personal identifiers</strong> are sent with your messages</li>
                  <li>• <strong className="text-purple-700">No conversation history</strong> is stored by us</li>
                  <li>• <strong className="text-purple-700">No user profiles</strong> are created or maintained</li>
                  <li>• Each conversation is <strong className="text-purple-700">independent and anonymous</strong></li>
                </ul>
              </div>
              <p className="text-center font-medium text-gray-700">
                We recommend reviewing the privacy policies of OpenAI and Anthropic for their data handling practices.
              </p>
            </div>
          </div>

          {/* Your Rights */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <h2 className="text-3xl font-bold text-purple-700 mb-6 text-center">Your Rights & Control</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="text-center">
                <div className="text-4xl mb-3">🗑️</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Delete Everything</h3>
                <p className="text-gray-600">Clear your browser data to remove all local storage instantly</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-3">🔒</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Private by Default</h3>
                <p className="text-gray-600">No accounts, no tracking, no data collection from day one</p>
              </div>
            </div>
          </div>

          {/* Contact */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 text-center">
            <h2 className="text-3xl font-bold text-purple-700 mb-4">Questions About Privacy?</h2>
            <p className="text-gray-700 mb-6">
              We're committed to transparency. Contact us with any privacy concerns.
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors font-medium"
            >
              Contact Privacy Team
            </a>
          </div>

        </div>
      </div>
    </div>
  );
};

export default Privacy;
