import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Spark<PERSON>, Heart, Shield, FileText, ArrowRight } from 'lucide-react';
import KittyAvatar from '../components/KittyAvatar';
import FloatingSparkles from '../components/FloatingSparkles';

const Welcome: React.FC = () => {
  const [isAnimating, setIsAnimating] = useState(true);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    // Start content animation after initial load
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-kitty-gradient-start via-kitty-gradient-middle to-kitty-gradient-end relative overflow-hidden">
      <FloatingSparkles />
      
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />

      <div className="relative z-10 container mx-auto px-4 py-8 min-h-screen flex flex-col justify-center">
        
        {/* Main Welcome Content */}
        <div className={`text-center transition-all duration-1000 ${showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          
          {/* Avatar */}
          <div className="flex justify-center mb-8">
            <div className="transform hover:scale-110 transition-transform duration-300">
              <KittyAvatar emotion="happy" isAnimating={isAnimating} />
            </div>
          </div>

          {/* Main Title */}
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 inline-block mb-8">
            <h1 className="text-5xl md:text-7xl font-bold text-gray-800 mb-4 animate-glow">
              Kitty AI
            </h1>
            <p className="text-2xl md:text-3xl text-gray-600 font-medium mb-6">
              Sovereign Wellness Companion
            </p>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
              The world's first emotionally intelligent AI companion, purpose-built for children and youth. 
              She responds to feelings, plays calming audio, and helps develop healthy self-care rituals — 
              all without collecting personal data.
            </p>
          </div>

          {/* CTA Button */}
          <div className="mb-12">
            <Link 
              to="/app"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 text-xl font-bold"
              onMouseEnter={() => setIsAnimating(true)}
              onMouseLeave={() => setIsAnimating(false)}
            >
              <Heart className="w-6 h-6" />
              Launch Kitty AI
              <ArrowRight className="w-6 h-6" />
            </Link>
          </div>

          {/* Feature Highlights */}
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
            <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-3">🧠</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Smart Empathy</h3>
              <p className="text-gray-600">AI that responds to your emotions with 30+ personalized reactions</p>
            </div>
            
            <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-3">🎵</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Mood Soundtracks</h3>
              <p className="text-gray-600">11 ambient tracks that adapt to how you're feeling</p>
            </div>
            
            <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 hover:scale-105 transition-transform duration-300">
              <div className="text-4xl mb-3">🛡️</div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">Privacy First</h3>
              <p className="text-gray-600">100% local storage - no data collection or monitoring</p>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              to="/about"
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
            >
              <Sparkles className="w-4 h-4" />
              About Kitty
            </Link>
            
            <Link 
              to="/press-kit"
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
            >
              <FileText className="w-4 h-4" />
              Press Kit
            </Link>
            
            <Link 
              to="/privacy"
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/90 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
            >
              <Shield className="w-4 h-4" />
              Privacy
            </Link>
          </div>

          {/* Version Badge */}
          <div className="mt-8">
            <div className="inline-block bg-gradient-to-r from-orange-100 to-red-100 rounded-full px-4 py-2 border border-orange-300">
              <span className="text-sm font-bold text-gray-800">
                🔥 FlameRelease 2.0 - Production Ready
              </span>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default Welcome;
