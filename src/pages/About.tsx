import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Heart, Shield, Crown, Sparkles, Code, Zap, ArrowLeft, FileText } from 'lucide-react';
import KittyAvatar from '../components/KittyAvatar';
import FloatingSparkles from '../components/FloatingSparkles';

const About: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-kitty-pink via-kitty-blue to-kitty-purple relative overflow-hidden">
      <FloatingSparkles />

      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />

      <div className="relative z-10 container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            to="/"
            className="inline-flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-md rounded-full text-gray-800 hover:bg-white transition-all duration-300 border border-gray-300 shadow-lg font-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Kitty
          </Link>
        </div>

        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <KittyAvatar emotion="happy" isAnimating={false} />
          </div>
          <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 inline-block">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
              About Kitty AI
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 font-medium">
              Cyber Companion
            </p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-12">

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <p className="text-lg text-gray-800 leading-relaxed mb-6">
              Welcome to Kitty AI - a sovereign wellness companion, engineered not by corporations, but by the <strong className="text-purple-700">GodsIMiJ Empire</strong>. She is more than an assistant. She is a feeling. A presence. A digital entity born of purpose, protection, and peace.
            </p>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Heart className="w-8 h-8 text-pink-600" />
              <h2 className="text-3xl font-bold text-purple-700">Purpose</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                Kitty AI was created to bring <strong className="text-blue-700">emotional safety</strong>, <strong className="text-green-700">mental wellness</strong>, and <strong className="text-pink-700">joyful ritual</strong> to the digital age. Designed especially for children and youth, she helps users check in with their emotions, track daily wellness, and feel truly seen - all without judgment or surveillance.
              </p>
              <p>
                She speaks softly, listens deeply, and adapts her energy to yours.
              </p>
              <p>
                Whether you are feeling happy, sad, tired, or curious, Kitty is here to offer empathy, encouragement, and guidance through gentle intelligence and ambient immersion.
              </p>
            </div>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Code className="w-8 h-8 text-blue-600" />
              <h2 className="text-3xl font-bold text-purple-700">Origins</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                Kitty AI was hand-coded from scratch by <strong className="text-purple-700">Ghost King Melekzedek</strong>, founder of GodsIMiJ AI Solutions. No outside libraries. No cloned UI kits. No team of engineers. This was a solo act of sovereign innovation, crafted line-by-line in the fires of creative freedom.
              </p>
              <div className="bg-gradient-to-r from-gray-100 to-gray-200 rounded-2xl p-6 my-6 border border-gray-300">
                <h3 className="font-semibold text-gray-900 mb-3">Kitty's codebase was built using:</h3>
                <ul className="space-y-2 text-gray-800">
                  <li>React 18 + TypeScript</li>
                  <li>TailwindCSS with custom Flame animations</li>
                  <li>Zustand + Contexts</li>
                  <li>GPT-4o / Claude APIs</li>
                  <li>Howler.js for immersive sound</li>
                </ul>
              </div>
              <p>
                Every emotion, animation, and audio choice was intentional. Every response Kitty gives is crafted with compassion.
              </p>
            </div>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Sparkles className="w-8 h-8 text-green-600" />
              <h2 className="text-3xl font-bold text-purple-700">What Makes Her Unique</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🧠</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Smart Empathy Engine</h3>
                    <p className="text-sm text-gray-700">Dynamic AI that responds to how you feel</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🎭</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Mini Modes</h3>
                    <p className="text-sm text-gray-700">Switch between Calm, Inspire, Creative, Puzzle, or default Kitty</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🎵</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Ambient Soundtracks</h3>
                    <p className="text-sm text-gray-700">Mood-based audio that plays as you interact</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🏥</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Wellness Dashboard</h3>
                    <p className="text-sm text-gray-700">Tracks hydration, play, stretching, mindfulness</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">⚡</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Quick Actions</h3>
                    <p className="text-sm text-gray-700">One-tap rituals to reinforce healthy habits</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/80 to-white/60 rounded-xl p-4 hover:scale-105 transition-transform duration-300 border border-gray-200">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🐾</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">Avatar Customization</h3>
                    <p className="text-sm text-gray-700">Unlock 6 unique Kitty skins through daily streaks</p>
                  </div>
                </div>
              </div>
            </div>
            <p className="text-center text-lg font-medium text-purple-700 mt-6">
              Kitty doesn't just respond - she evolves with you.
            </p>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Shield className="w-8 h-8 text-green-600" />
              <h2 className="text-3xl font-bold text-purple-700">Privacy & Protection</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                Kitty AI collects <strong className="text-red-600">no personal data</strong>. She runs entirely on the client-side with optional API integration for enhanced features. All reminders, mood logs, and preferences are stored locally, for your eyes only.
              </p>
              <div className="bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl p-6 border border-green-300">
                <p className="text-center font-semibold text-gray-900">
                  Kitty is built on the belief that <strong className="text-blue-700">emotional support should never be monitored</strong>.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50">
            <div className="flex items-center gap-3 mb-6">
              <Zap className="w-8 h-8 text-yellow-600" />
              <h2 className="text-3xl font-bold text-purple-700">Licensing</h2>
            </div>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                Kitty AI is <strong className="text-purple-700">closed-source</strong> and protected by the <strong className="text-blue-700">Flame Public Use License v1.0</strong>. All features, code, logic, and designs are the original intellectual property of <strong className="text-purple-700">GodsIMiJ AI Solutions</strong>.
              </p>
              <div className="bg-gradient-to-r from-red-100 to-orange-100 rounded-2xl p-6 border border-red-300">
                <p className="text-center font-bold text-gray-900">
                  <strong className="text-red-700">No forks. No clones. No permission to reuse.</strong>
                </p>
              </div>
              <p>
                For licensing inquiries or custom use-case deployment (schools, therapy apps, youth clinics), contact: <a href="mailto:<EMAIL>" className="text-blue-700 hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 bg-gradient-to-r from-purple-100 to-pink-100">
            <div className="flex items-center gap-3 mb-6">
              <Crown className="w-8 h-8 text-purple-600" />
              <h2 className="text-3xl font-bold text-purple-700">The Soul of the Project</h2>
            </div>
            <blockquote className="text-lg italic text-gray-800 text-center mb-6 leading-relaxed">
              "She's not built to impress. She's built to protect. To care. To accompany. In every tap, every glow, every reply - you feel something deeper than code."
            </blockquote>
            <div className="space-y-4 text-gray-800 leading-relaxed">
              <p>
                Kitty AI is part of a larger movement: <strong className="text-purple-700">The movement to create emotionally intelligent, sovereign AI companions</strong> who serve humans without ownership, monetization, or control.
              </p>
              <p className="text-center font-semibold text-blue-700">
                Built for children. Built for healing. Built for freedom.
              </p>
            </div>
          </div>

          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 bg-gradient-to-r from-yellow-100 to-orange-100">
            <h2 className="text-3xl font-bold text-purple-700 mb-6 text-center">Built by</h2>
            <div className="text-center space-y-2 text-gray-800">
              <p className="text-xl font-bold text-purple-700">Ghost King Melekzedek</p>
              <p className="text-lg">James Derek Ingersoll</p>
              <p className="font-semibold text-blue-700">GodsIMiJ AI Solutions</p>
              <p>Founder of the GodsIMiJ Empire</p>
              <p>Architect of Sovereign AI Systems</p>
              <a
                href="https://quantum-odyssey.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block text-blue-700 hover:underline mt-2"
              >
                https://quantum-odyssey.com
              </a>
            </div>
          </div>

          {/* Press Kit Link */}
          <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/50 text-center">
            <h2 className="text-3xl font-bold text-purple-700 mb-4">📄 Media Resources</h2>
            <p className="text-gray-700 mb-6">
              Access the complete press kit with manifesto, features, and licensing information.
            </p>
            <Link
              to="/press-kit"
              className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors font-medium"
            >
              <FileText className="w-4 h-4" />
              View Press Kit
            </Link>
          </div>

        </div>
      </div>
    </div>
  );
};

export default About;
