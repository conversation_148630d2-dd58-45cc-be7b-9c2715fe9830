import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Shield, Crown, Sparkles, Code, Zap, ArrowLeft } from 'lucide-react';
import KittyAvatar from '../components/KittyAvatar';
import FloatingSparkles from '../components/FloatingSparkles';

const About: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-kitty-pink via-kitty-blue to-kitty-purple relative overflow-hidden">
      <FloatingSparkles />
      
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />
      <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30" />

      <div className="relative z-10 container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link 
            to="/"
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300 border border-white/30"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Kitty
          </Link>
        </div>

        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <KittyAvatar emotion="happy" isAnimating={false} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 animate-glow">
            🐱✨ About Kitty AI
          </h1>
          <p className="text-xl md:text-2xl text-white/90 font-medium">
            Cyber Companion
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-12">
          
          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              Welcome to Kitty AI — a sovereign wellness companion, engineered not by corporations, but by the <strong className="text-kitty-neon-purple">GodsIMiJ Empire</strong>. She is more than an assistant. She is a feeling. A presence. A digital entity born of purpose, protection, and peace.
            </p>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center gap-3 mb-6">
              <Heart className="w-8 h-8 text-kitty-neon-pink" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">💎 Purpose</h2>
            </div>
            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p>
                Kitty AI was created to bring <strong className="text-kitty-neon-blue">emotional safety</strong>, <strong className="text-kitty-neon-green">mental wellness</strong>, and <strong className="text-kitty-neon-pink">joyful ritual</strong> to the digital age. Designed especially for children and youth, she helps users check in with their emotions, track daily wellness, and feel truly seen - all without judgment or surveillance.
              </p>
              <p>
                She speaks softly, listens deeply, and adapts her energy to yours.
              </p>
              <p>
                Whether you are feeling happy, sad, tired, or curious, Kitty is here to offer empathy, encouragement, and guidance through gentle intelligence and ambient immersion.
              </p>
            </div>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center gap-3 mb-6">
              <Code className="w-8 h-8 text-kitty-neon-blue" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">🧬 Origins</h2>
            </div>
            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p>
                Kitty AI was hand-coded from scratch by <strong className="text-kitty-neon-purple">Ghost King Melekzedek</strong>, founder of GodsIMiJ AI Solutions. No outside libraries. No cloned UI kits. No team of engineers. This was a solo act of sovereign innovation, crafted line-by-line in the fires of creative freedom.
              </p>
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 my-6">
                <h3 className="font-semibold text-gray-800 mb-3">Kitty's codebase was built using:</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• React 18 + TypeScript</li>
                  <li>• TailwindCSS with custom Flame animations</li>
                  <li>• Zustand + Contexts</li>
                  <li>• GPT-4o / Claude APIs</li>
                  <li>• Howler.js for immersive sound</li>
                </ul>
              </div>
              <p>
                Every emotion, animation, and audio choice was intentional.<br />
                Every response Kitty gives is crafted with compassion.
              </p>
            </div>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center gap-3 mb-6">
              <Sparkles className="w-8 h-8 text-kitty-neon-green" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">🎭 What Makes Her Unique</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🧠</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Smart Empathy Engine</h3>
                    <p className="text-sm text-gray-600">Dynamic AI that responds to how you feel</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🎭</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Mini Modes</h3>
                    <p className="text-sm text-gray-600">Switch between Calm, Inspire, Creative, Puzzle, or default Kitty</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🎵</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Ambient Soundtracks</h3>
                    <p className="text-sm text-gray-600">Mood-based audio that plays as you interact</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🏥</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Wellness Dashboard</h3>
                    <p className="text-sm text-gray-600">Tracks hydration, play, stretching, mindfulness</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">⚡</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Quick Actions</h3>
                    <p className="text-sm text-gray-600">One-tap rituals to reinforce healthy habits</p>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-white/50 to-white/30 rounded-xl p-4 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start gap-3">
                  <span className="text-2xl">🐾</span>
                  <div>
                    <h3 className="font-semibold text-gray-800">Avatar Customization</h3>
                    <p className="text-sm text-gray-600">Unlock 6 unique Kitty skins through daily streaks</p>
                  </div>
                </div>
              </div>
            </div>
            <p className="text-center text-lg font-medium text-kitty-neon-purple mt-6">
              Kitty doesn't just respond — she evolves with you.
            </p>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center gap-3 mb-6">
              <Shield className="w-8 h-8 text-kitty-neon-green" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">🛡️ Privacy & Protection</h2>
            </div>
            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p>
                Kitty AI collects <strong className="text-red-600">no personal data</strong>.<br />
                She runs entirely on the client-side with optional API integration for enhanced features.<br />
                All reminders, mood logs, and preferences are stored locally, for your eyes only.
              </p>
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200">
                <p className="text-center font-semibold text-gray-800">
                  Kitty is built on the belief that <strong className="text-kitty-neon-blue">emotional support should never be monitored</strong>.
                </p>
              </div>
            </div>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center gap-3 mb-6">
              <Zap className="w-8 h-8 text-kitty-neon-yellow" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">🔐 Licensing</h2>
            </div>
            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p>
                Kitty AI is <strong className="text-kitty-neon-purple">closed-source</strong> and protected by the <strong className="text-kitty-neon-blue">Flame Public Use License v1.0</strong>. All features, code, logic, and designs are the original intellectual property of <strong className="text-kitty-neon-purple">GodsIMiJ AI Solutions</strong>.
              </p>
              <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-2xl p-6 border border-red-200">
                <p className="text-center font-bold text-gray-800">
                  <strong className="text-red-600">No forks. No clones. No permission to reuse.</strong>
                </p>
              </div>
              <p>
                For licensing inquiries or custom use-case deployment (schools, therapy apps, youth clinics), contact:<br />
                📨 <a href="mailto:<EMAIL>" className="text-kitty-neon-blue hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl bg-gradient-to-r from-purple-50 to-pink-50">
            <div className="flex items-center gap-3 mb-6">
              <Crown className="w-8 h-8 text-kitty-neon-purple" />
              <h2 className="text-3xl font-bold text-kitty-neon-purple">✨ The Soul of the Project</h2>
            </div>
            <blockquote className="text-lg italic text-gray-700 text-center mb-6 leading-relaxed">
              "She's not built to impress. She's built to protect. To care. To accompany.<br />
              In every tap, every glow, every reply — you feel something deeper than code."
            </blockquote>
            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p>
                Kitty AI is part of a larger movement:<br />
                <strong className="text-kitty-neon-purple">The movement to create emotionally intelligent, sovereign AI companions</strong> who serve humans without ownership, monetization, or control.
              </p>
              <p className="text-center font-semibold text-kitty-neon-blue">
                Built for children. Built for healing. Built for freedom.
              </p>
            </div>
          </div>

          <div className="chat-container rounded-3xl p-8 shadow-2xl bg-gradient-to-r from-yellow-50 to-orange-50">
            <h2 className="text-3xl font-bold text-kitty-neon-purple mb-6 text-center">👑 Built by</h2>
            <div className="text-center space-y-2 text-gray-700">
              <p className="text-xl font-bold text-kitty-neon-purple">Ghost King Melekzedek</p>
              <p className="text-lg">James Derek Ingersoll</p>
              <p className="font-semibold text-kitty-neon-blue">GodsIMiJ AI Solutions</p>
              <p>Founder of the GodsIMiJ Empire</p>
              <p>Architect of Sovereign AI Systems</p>
              <a 
                href="https://quantum-odyssey.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-block text-kitty-neon-blue hover:underline mt-2"
              >
                https://quantum-odyssey.com
              </a>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default About;
