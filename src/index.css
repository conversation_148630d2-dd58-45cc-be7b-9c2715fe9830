
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 30% 98%;
    --foreground: 240 10% 15%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 15%;

    --primary: 340 82% 65%;
    --primary-foreground: 0 0% 98%;

    --secondary: 200 20% 94%;
    --secondary-foreground: 240 10% 15%;

    --muted: 200 20% 94%;
    --muted-foreground: 240 5% 50%;

    --accent: 200 20% 94%;
    --accent-foreground: 240 10% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 340 82% 65%;

    --radius: 1rem;

    --sidebar-background: 220 30% 98%;
    --sidebar-foreground: 240 10% 15%;
    --sidebar-primary: 340 82% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 200 20% 94%;
    --sidebar-accent-foreground: 240 10% 15%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 340 82% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background: linear-gradient(135deg, #B0E0E6 0%, #F0FFF0 25%, #FFB6C1 50%, #DDA0DD 75%, #FFFFE0 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
    overflow-x: hidden;
  }

  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(255, 105, 180, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(147, 112, 219, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  .chat-container {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .chat-bubble {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .chat-bubble.typing-glow {
    border: 2px solid rgba(255, 105, 180, 0.5);
    animation: typingPulse 2s ease-in-out infinite;
  }

  .kitty-glow {
    box-shadow: 0 0 30px rgba(255, 105, 180, 0.4), 0 0 60px rgba(255, 105, 180, 0.2);
  }

  .neon-outline {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #FF69B4, #00BFFF, #9370DB) border-box;
    animation: neonRotate 3s linear infinite;
  }

  .floating-sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #FF69B4, transparent);
    border-radius: 50%;
    animation: sparkleFloat 4s ease-in-out infinite;
  }

  .mood-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 10;
  }

  .mood-button:hover .mood-tooltip {
    opacity: 1;
  }

  .voice-wave {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .voice-wave-bar {
    width: 3px;
    background: currentColor;
    border-radius: 2px;
    animation: voiceWave 1.5s ease-in-out infinite;
  }

  .voice-wave-bar:nth-child(1) { height: 8px; animation-delay: 0s; }
  .voice-wave-bar:nth-child(2) { height: 12px; animation-delay: 0.1s; }
  .voice-wave-bar:nth-child(3) { height: 16px; animation-delay: 0.2s; }
  .voice-wave-bar:nth-child(4) { height: 12px; animation-delay: 0.3s; }
  .voice-wave-bar:nth-child(5) { height: 8px; animation-delay: 0.4s; }

  /* Phase II-D Animations */
  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  .animate-progress-fill {
    animation: progress-fill 0.5s ease-out;
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes progress-fill {
  0% {
    width: 0%;
  }
  100% {
    width: var(--progress-width);
  }
}
