
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 30% 98%;
    --foreground: 240 10% 15%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 15%;

    --primary: 340 82% 65%;
    --primary-foreground: 0 0% 98%;

    --secondary: 200 20% 94%;
    --secondary-foreground: 240 10% 15%;

    --muted: 200 20% 94%;
    --muted-foreground: 240 5% 50%;

    --accent: 200 20% 94%;
    --accent-foreground: 240 10% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 340 82% 65%;

    --radius: 1rem;

    --sidebar-background: 220 30% 98%;
    --sidebar-foreground: 240 10% 15%;
    --sidebar-primary: 340 82% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 200 20% 94%;
    --sidebar-accent-foreground: 240 10% 15%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 340 82% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-kitty-blue via-kitty-mint to-kitty-pink text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  .chat-bubble {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .kitty-glow {
    box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
  }

  .neon-outline {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #FF69B4, #00BFFF) border-box;
  }
}
