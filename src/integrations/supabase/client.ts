// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lbeeugrwtafiepsdoqmx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxiZWV1Z3J3dGFmaWVwc2RvcW14Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MjkyMTAsImV4cCI6MjA2MDMwNTIxMH0.7Tp_rLMERmVkY4BTBqQ66i4uj2NCMu1Dd9pqejZs_vc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);