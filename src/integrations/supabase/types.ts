export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      analytics: {
        Row: {
          created_at: string
          event_type: string
          id: string
          metadata: <PERSON><PERSON> | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          event_type: string
          id?: string
          metadata?: Json | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          event_type?: string
          id?: string
          metadata?: Json | null
          user_id?: string | null
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          content: string
          created_at: string
          id: string
          role: string
          thread_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          role: string
          thread_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          role?: string
          thread_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_thread_id_fkey"
            columns: ["thread_id"]
            isOneToOne: false
            referencedRelation: "chat_threads"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_threads: {
        Row: {
          created_at: string
          id: string
          mode: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          mode?: string
          title?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          mode?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      clients: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      crash_events: {
        Row: {
          chaos_level: number
          created_at: string
          id: string
          ritual_state: Json | null
          stack_trace: string
          updated_at: string
        }
        Insert: {
          chaos_level: number
          created_at?: string
          id?: string
          ritual_state?: Json | null
          stack_trace: string
          updated_at?: string
        }
        Update: {
          chaos_level?: number
          created_at?: string
          id?: string
          ritual_state?: Json | null
          stack_trace?: string
          updated_at?: string
        }
        Relationships: []
      }
      customers: {
        Row: {
          created_at: string | null
          current_period_end: string | null
          email: string
          id: string
          plan: Database["public"]["Enums"]["subscription_plan"] | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          current_period_end?: string | null
          email: string
          id?: string
          plan?: Database["public"]["Enums"]["subscription_plan"] | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_status?:
            | Database["public"]["Enums"]["subscription_status"]
            | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          current_period_end?: string | null
          email?: string
          id?: string
          plan?: Database["public"]["Enums"]["subscription_plan"] | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_status?:
            | Database["public"]["Enums"]["subscription_status"]
            | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      device_chat_messages: {
        Row: {
          content: string
          created_at: string
          device_id: string
          id: string
          mode: string
          type: string
        }
        Insert: {
          content: string
          created_at?: string
          device_id: string
          id?: string
          mode: string
          type: string
        }
        Update: {
          content?: string
          created_at?: string
          device_id?: string
          id?: string
          mode?: string
          type?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          client_id: string
          content: Json
          created_at: string
          id: string
          status: string | null
          title: string
          total_amount: number | null
          type: Database["public"]["Enums"]["document_type"]
          updated_at: string
          user_id: string
        }
        Insert: {
          client_id: string
          content: Json
          created_at?: string
          id?: string
          status?: string | null
          title: string
          total_amount?: number | null
          type: Database["public"]["Enums"]["document_type"]
          updated_at?: string
          user_id: string
        }
        Update: {
          client_id?: string
          content?: Json
          created_at?: string
          id?: string
          status?: string | null
          title?: string
          total_amount?: number | null
          type?: Database["public"]["Enums"]["document_type"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      heresy_logs: {
        Row: {
          content: string
          created_at: string
          entropy_level: number
          id: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          entropy_level: number
          id?: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          entropy_level?: number
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      invoice_items: {
        Row: {
          amount: number
          created_at: string
          description: string
          id: string
          invoice_id: string
          quantity: number
          unit_price: number
        }
        Insert: {
          amount: number
          created_at?: string
          description: string
          id?: string
          invoice_id: string
          quantity?: number
          unit_price: number
        }
        Update: {
          amount?: number
          created_at?: string
          description?: string
          id?: string
          invoice_id?: string
          quantity?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          client_id: string
          created_at: string
          due_date: string
          id: string
          invoice_number: string
          issue_date: string
          notes: string | null
          status: Database["public"]["Enums"]["invoice_status"]
          subtotal: number
          tax_amount: number
          tax_rate: number
          total_amount: number
          updated_at: string
          user_id: string
        }
        Insert: {
          client_id: string
          created_at?: string
          due_date: string
          id?: string
          invoice_number: string
          issue_date?: string
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          subtotal?: number
          tax_amount?: number
          tax_rate?: number
          total_amount?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          client_id?: string
          created_at?: string
          due_date?: string
          id?: string
          invoice_number?: string
          issue_date?: string
          notes?: string | null
          status?: Database["public"]["Enums"]["invoice_status"]
          subtotal?: number
          tax_amount?: number
          tax_rate?: number
          total_amount?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoices_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      loops: {
        Row: {
          bpm: number
          created_at: string
          genres: string[]
          id: string
          is_favorite: boolean
          loop_length: string
          mood: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          bpm: number
          created_at?: string
          genres?: string[]
          id?: string
          is_favorite?: boolean
          loop_length: string
          mood?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          bpm?: number
          created_at?: string
          genres?: string[]
          id?: string
          is_favorite?: boolean
          loop_length?: string
          mood?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      mixboard_sessions: {
        Row: {
          created_at: string
          id: string
          name: string
          tracks: Json
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name?: string
          tracks: Json
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          tracks?: Json
          user_id?: string
        }
        Relationships: []
      }
      notes: {
        Row: {
          content: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          content?: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      path_rituals: {
        Row: {
          created_at: string
          id: string
          path: string
          ritual_performed: string
          updated_at: string
          void_essence: string
        }
        Insert: {
          created_at?: string
          id?: string
          path: string
          ritual_performed: string
          updated_at?: string
          void_essence: string
        }
        Update: {
          created_at?: string
          id?: string
          path?: string
          ritual_performed?: string
          updated_at?: string
          void_essence?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          full_name: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          full_name?: string | null
          id: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      ritual_videos: {
        Row: {
          audio_url: string | null
          created_at: string | null
          id: string
          status: Database["public"]["Enums"]["ritual_status"] | null
          title: string
          updated_at: string | null
          user_id: string
          video_url: string | null
          visual_style: string
        }
        Insert: {
          audio_url?: string | null
          created_at?: string | null
          id?: string
          status?: Database["public"]["Enums"]["ritual_status"] | null
          title: string
          updated_at?: string | null
          user_id: string
          video_url?: string | null
          visual_style: string
        }
        Update: {
          audio_url?: string | null
          created_at?: string | null
          id?: string
          status?: Database["public"]["Enums"]["ritual_status"] | null
          title?: string
          updated_at?: string | null
          user_id?: string
          video_url?: string | null
          visual_style?: string
        }
        Relationships: []
      }
      scheduled_posts: {
        Row: {
          caption: string
          created_at: string | null
          id: string
          media_url: string
          platforms: string[]
          scheduled_for: string
          status: string
          updated_at: string | null
          vibe: string
        }
        Insert: {
          caption: string
          created_at?: string | null
          id?: string
          media_url: string
          platforms: string[]
          scheduled_for: string
          status: string
          updated_at?: string | null
          vibe: string
        }
        Update: {
          caption?: string
          created_at?: string | null
          id?: string
          media_url?: string
          platforms?: string[]
          scheduled_for?: string
          status?: string
          updated_at?: string | null
          vibe?: string
        }
        Relationships: []
      }
      sessions: {
        Row: {
          created_at: string
          fx_settings: Json | null
          id: string
          pads_hit: Json
          session_name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          fx_settings?: Json | null
          id?: string
          pads_hit: Json
          session_name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          fx_settings?: Json | null
          id?: string
          pads_hit?: Json
          session_name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      shared_loops: {
        Row: {
          created_at: string
          id: string
          loop_id: string | null
          short_code: string
          views: number
        }
        Insert: {
          created_at?: string
          id?: string
          loop_id?: string | null
          short_code?: string
          views?: number
        }
        Update: {
          created_at?: string
          id?: string
          loop_id?: string | null
          short_code?: string
          views?: number
        }
        Relationships: [
          {
            foreignKeyName: "shared_loops_loop_id_fkey"
            columns: ["loop_id"]
            isOneToOne: false
            referencedRelation: "loops"
            referencedColumns: ["id"]
          },
        ]
      }
      sigils: {
        Row: {
          claimed_by: string | null
          created_at: string
          entropy_hash: string
          heresy_seed: string
          id: string
          svg_data: string
          updated_at: string
        }
        Insert: {
          claimed_by?: string | null
          created_at?: string
          entropy_hash: string
          heresy_seed: string
          id?: string
          svg_data: string
          updated_at?: string
        }
        Update: {
          claimed_by?: string | null
          created_at?: string
          entropy_hash?: string
          heresy_seed?: string
          id?: string
          svg_data?: string
          updated_at?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          cancelatperiodend: boolean | null
          created_at: string | null
          currentperiodend: string | null
          currentperiodstart: string | null
          id: string
          plantier: string
          status: string
          stripecustomerid: string
          userid: string
        }
        Insert: {
          cancelatperiodend?: boolean | null
          created_at?: string | null
          currentperiodend?: string | null
          currentperiodstart?: string | null
          id?: string
          plantier?: string
          status?: string
          stripecustomerid: string
          userid: string
        }
        Update: {
          cancelatperiodend?: boolean | null
          created_at?: string | null
          currentperiodend?: string | null
          currentperiodstart?: string | null
          id?: string
          plantier?: string
          status?: string
          stripecustomerid?: string
          userid?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_event_counts: {
        Args: Record<PropertyKey, never>
        Returns: {
          event_type: string
          count: number
        }[]
      }
      has_role: {
        Args: {
          requested_user_id: string
          requested_role: Database["public"]["Enums"]["app_role"]
        }
        Returns: boolean
      }
      increment_share_views: {
        Args: { share_id: string }
        Returns: undefined
      }
      update_thread_title: {
        Args: { thread_id_arg: string; new_title_arg: string }
        Returns: boolean
      }
    }
    Enums: {
      app_role: "admin" | "user"
      document_type: "quote" | "invoice" | "receipt"
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
      ritual_status: "queued" | "processing" | "complete" | "failed"
      ritual_type: "sigil" | "heresy" | "crash" | "404"
      subscription_plan: "free" | "scribe" | "author"
      subscription_status: "trial" | "active" | "canceled" | "expired"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "user"],
      document_type: ["quote", "invoice", "receipt"],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
      ritual_status: ["queued", "processing", "complete", "failed"],
      ritual_type: ["sigil", "heresy", "crash", "404"],
      subscription_plan: ["free", "scribe", "author"],
      subscription_status: ["trial", "active", "canceled", "expired"],
    },
  },
} as const
